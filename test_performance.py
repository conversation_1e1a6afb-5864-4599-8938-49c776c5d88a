# -*- coding: utf-8 -*-
"""
测试版本 - 不依赖YOLO模型，用于测试性能优化效果
主要用于验证FPS优化是否有效
"""

import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque
import subprocess
import logging
import json
import random
import math

# 模拟的轨迹线渲染器（简化版）
class MockTrajectoryRenderer:
    def __init__(self, enable_dynamic_sizing=True, length_multiplier=2.0, width_ratio=0.5):
        self.enable_dynamic_sizing = enable_dynamic_sizing
        self.length_multiplier = length_multiplier
        self.width_ratio = width_ratio
        
    def render_trajectory(self, frame, heading_angle):
        # 简单绘制两条线作为模拟轨迹
        height, width = frame.shape[:2]
        center_x = width // 2
        start_y = height
        end_y = int(height * 0.3)
        
        # 绘制两条平行线
        offset = int(width * 0.1)
        cv2.line(frame, (center_x - offset, start_y), (center_x - offset, end_y), (0, 0, 255), 3)
        cv2.line(frame, (center_x + offset, start_y), (center_x + offset, end_y), (0, 0, 255), 3)
        
        return frame
        
    def get_trajectory_boundaries(self, center_x, start_y, heading_angle, frame_width, frame_height, target_y):
        # 模拟返回边界
        offset = int(frame_width * 0.1)
        return {
            'red_left': center_x - offset,
            'red_right': center_x + offset,
            'white_left': center_x - offset * 2,
            'white_right': center_x + offset * 2
        }
    
    def set_manual_params(self, length=None, width=None):
        pass

# 模拟检测结果
class MockDetectionResult:
    def __init__(self):
        # 随机生成1-3个检测框
        num_boxes = random.randint(0, 3)
        self.boxes = []
        
        for _ in range(num_boxes):
            box = MockBox()
            self.boxes.append(box)
    
    def plot(self, img, conf=True, line_width=2, font_size=0.5):
        # 在图像上绘制检测框
        for box in self.boxes:
            x1, y1, x2, y2 = box.xyxy[0]
            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            
            # 绘制检测框
            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), line_width)
            
            if conf:
                label = f'Class_{box.cls} {box.conf:.2f}'
                cv2.putText(img, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, font_size, (0, 255, 0), 1)
        
        return img

class MockBox:
    def __init__(self):
        # 随机生成检测框坐标 (针对640x360分辨率)
        x1 = random.randint(50, 500)
        y1 = random.randint(50, 250)
        x2 = x1 + random.randint(50, 150)
        y2 = y1 + random.randint(50, 100)
        
        self.xyxy = [[x1, y1, x2, y2]]
        self.cls = random.randint(0, 4)  # 5类物体
        self.conf = random.uniform(0.5, 0.95)

# 模拟YOLO模型
class MockYOLO:
    def __init__(self, model_path):
        self.model_path = model_path
        print(f"模拟加载模型: {model_path}")
        time.sleep(0.1)  # 模拟加载时间
    
    def to(self, device):
        print(f"模拟将模型移动到设备: {device}")
        return self
    
    def track(self, frame, conf=0.45, show_conf=False, verbose=False, tracker='bytetrack.yaml'):
        # 模拟推理时间 (根据输入分辨率调整)
        height, width = frame.shape[:2]
        if width <= 640:
            time.sleep(0.02)  # 640x360推理时间约20ms
        else:
            time.sleep(0.05)  # 1920x1080推理时间约50ms
        
        # 返回模拟检测结果
        return [MockDetectionResult()]

# 模拟其他依赖函数
def drone_yaw_get(dict_json):
    # 模拟返回航向角
    return random.uniform(-180, 180)

def drone_state_get(dict_json):
    return {"state": "flying", "battery": 80}

class MockMinioUpdate:
    @staticmethod
    def minio_interface(dict_json, type_str, filename, filepath):
        print(f"模拟上传到MinIO: {type_str} - {filename}")
        return f"minio_url_{filename}"

# 模拟的 minio_update 和 drones_server
class MockModule:
    def __getattr__(self, name):
        return lambda *args, **kwargs: print(f"调用模拟函数: {name}")

logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    handlers=[
        logging.FileHandler("test_performance.log"),
        logging.StreamHandler()
    ]
)

class multiDealImg(object):
    model = None
    model_lock = threading.Lock()

    def __init__(self, model_path, save_path, dict_json):
        if multiDealImg.model is None:
            multiDealImg.model = MockYOLO(model_path)  # 使用模拟模型
        self.model = multiDealImg.model
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化的队列大小
        self.save_queue = queue.Queue(maxsize=5)
        self.push_queue = queue.Queue(maxsize=2)
        self.frame_queue = queue.Queue(maxsize=2)
        
        # 推理控制参数
        self.inference_interval = 2  # 每2帧进行一次推理
        self.frame_counter = 0
        self.last_results = None

        self.names = ["excavator", "crane", "compactor", "tank truck", "loader"]
        self.results = None
        self.dataT = 2
        
        # 告警相关
        self.buffer = deque(maxlen=75)
        self.alert_flag = False
        self.alert_frames = []
        self.alert_counter = 0
        self.alert_lock = threading.Lock()
        self.alert_info = {}
        
        self.drone_state = {}
        self.drone_angle = 0
        
        # 使用模拟轨迹渲染器
        self.trajectory_renderer = MockTrajectoryRenderer(
            enable_dynamic_sizing=True,
            length_multiplier=2.0,
            width_ratio=0.5
        )

    def open_ffmpeg_process(self, output_stream):
        print(f"模拟开启FFmpeg推流进程到: {output_stream}")
        # 对于测试，我们不实际启动FFmpeg，只是模拟
        self.pipe = None
        self.mock_push_time = time.time()

    def openFfmpegSaveVideo(self, outputSaveVideo):
        print(f"模拟开启FFmpeg保存视频到: {outputSaveVideo}")
        self.ffmpegSaveVideo = None

    def read_video(self, video_path):
        print(f"开始读取视频: {video_path}")
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            logging.error(f"无法打开视频: {video_path}")
            return
    
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
    
        try:
            count = 0
            skip_frames = 0
            fps_counter = 0
            fps_timer = time.time()
            
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:
                    logging.info(f"视频结束，重新播放: {video_path}")
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 循环播放用于测试
                    continue
    
                if frame is None or frame.size == 0:
                    continue
                
                # 跳帧处理
                skip_frames += 1
                if skip_frames % 3 != 0:
                    continue
    
                try:
                    frame = cv2.resize(frame, (1920, 1080))
                except Exception as e:
                    logging.error(f"帧缩放失败: {e}")
                    continue
    
                # 非阻塞队列操作
                try:
                    self.frame_queue.put_nowait(frame)
                except queue.Full:
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame)
                    except queue.Empty:
                        pass
                
                # FPS统计
                fps_counter += 1
                if fps_counter % 100 == 0:
                    elapsed = time.time() - fps_timer
                    read_fps = fps_counter / elapsed
                    logging.info(f"视频读取FPS: {read_fps:.2f}")
                    fps_counter = 0
                    fps_timer = time.time()
                
                count += 1
                if count % 10 == 0:
                    self.drone_angle = drone_yaw_get(self.dict_json)
                    
        except Exception as e:
            logging.error(f"视频读取错误: {e}")
        finally:
            cap.release()
            logging.info(f"视频资源已释放: {video_path}")

    def process_frame(self):
        try:
            count = 0
            alarmLevel = "2"
            alarmClass = "excavator"
            
            fps_counter = 0
            fps_timer = time.time()
    
            while self.is_switchOn:
                try:
                    frame = self.frame_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                    
                t1 = time.time()
                
                # 控制推理频率
                self.frame_counter += 1
                should_infer = (self.frame_counter % self.inference_interval == 0)
                
                if should_infer:
                    # 使用较小分辨率进行推理
                    inference_frame = cv2.resize(frame, (640, 360))
                    with self.model_lock:
                        self.results = self.model.track(
                            inference_frame, 
                            conf=0.5,
                            show_conf=False, 
                            verbose=False, 
                            tracker='bytetrack.yaml'
                        )
                        self.last_results = self.results
                else:
                    self.results = self.last_results
                
                # 简化的告警检测
                alert_triggered = False
                if self.results and len(self.results) > 0:
                    result = self.results[0]
                    if hasattr(result, 'boxes') and result.boxes and len(result.boxes) > 0:
                        for box in result.boxes:
                            cls = int(box.cls)
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            center_x = (x1 + x2) // 2
                            
                            # 简化告警判断
                            frame_center_x = 960  # 1920/2
                            distance_from_center = abs(center_x * 3 - frame_center_x)  # 缩放回原尺寸
                            
                            if distance_from_center < 200:
                                alarmLevel = "1"
                                alert_triggered = True
                            elif distance_from_center < 400:
                                alarmLevel = "2"
                                alert_triggered = True
                            else:
                                alarmLevel = "0"
                                
                            alarmClass = self.names[cls]
                            if alert_triggered:
                                break
    
                # 简化告警处理
                with self.alert_lock:
                    if alert_triggered and not self.alert_flag:
                        self.alert_frames = list(self.buffer)
                        self.alert_flag = True
                        self.alert_counter = 0
    
                    self.buffer.append(frame.copy())
    
                    if self.alert_flag:
                        self.alert_frames.append(frame.copy())
                        self.alert_counter += 1
                        if self.alert_counter >= 125:
                            logging.info(f"模拟保存告警视频: {alarmLevel}, {alarmClass}")
                            self.alert_flag = False
                            self.alert_frames = []
                            self.alert_counter = 0
    
                # 保存队列
                try:
                    self.save_queue.put_nowait(frame.copy())
                except queue.Full:
                    pass
                
                # 创建显示帧
                display_frame = frame.copy()
                
                # 定期更新轨迹线
                if count % 5 == 0:
                    display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)
                
                # 绘制检测框
                try:
                    if self.results and should_infer and len(self.results) > 0:
                        result = self.results[0]
                        if hasattr(result, 'boxes') and result.boxes:
                            scale_x, scale_y = 1920 / 640, 1080 / 360
                            
                            for box in result.boxes:
                                x1, y1, x2, y2 = box.xyxy[0]
                                x1, y1 = int(x1*scale_x), int(y1*scale_y)
                                x2, y2 = int(x2*scale_x), int(y2*scale_y)
                                cls = int(box.cls)
                                conf = float(box.conf)
                                
                                cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                label = f'{self.names[cls]} {conf:.2f}'
                                cv2.putText(display_frame, label, (x1, y1-10), 
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                except Exception as e:
                    logging.error(f"检测框绘制错误: {e}")
                
                # 推流队列
                try:
                    self.push_queue.put_nowait(display_frame)
                except queue.Full:
                    try:
                        self.push_queue.get_nowait()
                        self.push_queue.put_nowait(display_frame)
                    except queue.Empty:
                        pass
                
                self.dataT = time.time() - t1
                
                # FPS统计
                fps_counter += 1
                if fps_counter % 100 == 0:
                    elapsed = time.time() - fps_timer
                    process_fps = fps_counter / elapsed
                    logging.info(f"处理FPS: {process_fps:.2f}, 推理时间: {self.dataT:.4f}s, 队列大小: frame={self.frame_queue.qsize()}, push={self.push_queue.qsize()}")
                    fps_counter = 0
                    fps_timer = time.time()
                    
                count += 1
                
        except Exception as e:
            logging.error(f"帧处理错误: {e}")

    def pushImg(self):
        try:
            frame_count = 0
            target_fps = 25
            frame_interval = 1 / target_fps
            last_frame_time = time.time()
            
            fps_counter = 0
            fps_timer = time.time()
            
            while self.is_switchOn:
                t_start = time.time()
                
                try:
                    frame = self.push_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # 模拟推流处理时间
                time.sleep(0.005)  # 模拟5ms的推流处理时间
                
                frame_count += 1
                elapsed = time.time() - t_start
                
                # FPS统计
                fps_counter += 1
                if fps_counter % 100 == 0:
                    elapsed_fps = time.time() - fps_timer
                    push_fps = fps_counter / elapsed_fps
                    logging.info(f"推流FPS: {push_fps:.2f}, 推流时间: {elapsed:.4f}s")
                    fps_counter = 0
                    fps_timer = time.time()
                
                # 帧率控制
                next_frame_time = last_frame_time + frame_interval
                sleep_time = max(0, next_frame_time - time.time())
                if sleep_time > 0:
                    time.sleep(sleep_time)
                last_frame_time = time.time()
                        
        except Exception as e:
            logging.error(f"推流错误: {e}")

    def saveImg(self):
        try:
            fps_counter = 0
            fps_timer = time.time()
            
            while self.is_switchOn or not self.save_queue.empty():
                try:
                    frame = self.save_queue.get(timeout=1.0)
                    # 模拟保存处理时间
                    time.sleep(0.003)  # 模拟3ms的保存处理时间
                    
                    fps_counter += 1
                    if fps_counter % 100 == 0:
                        elapsed = time.time() - fps_timer
                        save_fps = fps_counter / elapsed
                        logging.info(f"保存FPS: {save_fps:.2f}")
                        fps_counter = 0
                        fps_timer = time.time()
                        
                except queue.Empty:
                    continue
                except Exception as e:
                    logging.error(f"视频保存失败: {e}")
                    break
    
            logging.info("视频保存完成，模拟上传到MinIO")
    
        except Exception as e:
            logging.error(f"视频保存错误: {e}")

    def set_switch_on(self, listen_value):
        self.is_switchOn = listen_value
        
    def set_video_name(self, video_name):
        self.video_name = video_name

    def set_mission_id(self, mission_id):
        self.mission_id = mission_id

    def check_pipe_health(self):
        while self.is_switchOn:
            time.sleep(5)
            
    def startThread(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg),
                threading.Thread(target=self.check_pipe_health)
            ]
            
            for t in threads:
                t.daemon = True
                t.start()
            
            # 主线程监控
            try:
                while self.is_switchOn:
                    time.sleep(1)
                    # 每10秒输出一次状态
                    if int(time.time()) % 10 == 0:
                        logging.info(f"系统运行中 - 队列状态: frame={self.frame_queue.qsize()}, push={self.push_queue.qsize()}, save={self.save_queue.qsize()}")
                        time.sleep(1)  # 避免重复输出
                        
            except KeyboardInterrupt:
                logging.info("收到停止信号，正在关闭线程...")
                self.is_switchOn = False
                
            for t in threads:
                t.join(timeout=5)  # 最多等待5秒
                
        except Exception as e:
            logging.error(f"线程启动失败: {e}")

def test_performance():
    """性能测试函数"""
    print("开始性能测试...")
    
    # 测试参数
    model_path = "mock_model.pt"
    save_path = "./test_output.mp4"
    
    # 模拟的输入参数
    dict_json = {
        "droneStreamUrl": "test_video.mp4",  # 请替换为你的测试视频路径
        "aiStreamUrl": "rtmp://test/output",
        "taskId": "test_task_001"
    }
    
    # 创建检测器实例
    detector = multiDealImg(model_path, save_path, dict_json)
    detector.set_switch_on(True)
    detector.set_video_name("test_video")
    
    print("启动测试线程...")
    print("按 Ctrl+C 停止测试")
    
    try:
        detector.startThread(
            dict_json["droneStreamUrl"],
            dict_json["aiStreamUrl"], 
            save_path
        )
    except KeyboardInterrupt:
        print("测试停止")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        detector.set_switch_on(False)

if __name__ == "__main__":
    # 在这里设置你的测试视频路径
    print("=== 性能测试版本 ===")
    print("请将测试视频文件名修改为 test_video.mp4 并放在当前目录")
    print("或者修改 dict_json['droneStreamUrl'] 为你的视频文件路径")
    test_performance()
