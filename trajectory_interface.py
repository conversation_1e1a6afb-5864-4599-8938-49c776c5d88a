import cv2
import numpy as np
import math
import time
from PIL import Image, ImageDraw, ImageFont
from typing import Optional, Tuple


class TrajectoryRenderer:
    """轨迹线渲染接口类"""
    
    def __init__(self, video_path: Optional[str] = None, bottom_width: int = 200, top_width: int = 50, 
                 enable_dynamic_sizing: bool = True, length_multiplier: float = 8.0, width_ratio: float = 0.5):
        """
        初始化轨迹线渲染器
        
        Args:
            video_path: 视频文件路径，如果为None则使用黑色背景
            bottom_width: 轨迹线底部间距（像素）
            top_width: 轨迹线顶部间距（像素）
            enable_dynamic_sizing: 是否启用动态尺寸调整
            length_multiplier: 轨迹线长度为视频高度的倍数（默认2.0倍）
            width_ratio: 轨迹线间距为视频宽度的比例（默认0.5，即一半）
        """
        self.video_path = video_path
        self.bottom_width = bottom_width
        self.top_width = top_width
        self.use_video = False
        self.cap = None
        self.width = 800
        self.height = 600
        self.fps = 30
        
        # 动态尺寸调整参数
        self.enable_dynamic_sizing = enable_dynamic_sizing
        self.length_multiplier = length_multiplier  # 轨迹线长度为视频高度的倍数
        self.width_ratio = width_ratio  # 轨迹线间距为视频宽度的比例
        self.manual_length = None  # 手动设置的轨迹线长度
        self.manual_width = None   # 手动设置的轨迹线宽度
        
        # 虚线样式参数
        self.dash_length = 20  # 虚线段长度
        self.gap_length = 10   # 虚线间隔长度
        
        # 字体缓存
        self._font_large = None
        self._font_small = None
        
        self._initialize_video()
        self._load_fonts()
    
    def _initialize_video(self):
        """初始化视频捕获"""
        if self.video_path:
            try:
                # 检查是否是RTMP流
                if self.video_path.startswith('rtmp://'):
                    print(f"正在连接RTMP流: {self.video_path}")
                    self.cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                    # 设置RTMP参数
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)
                    self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
                else:
                    self.cap = cv2.VideoCapture(self.video_path)
                
                if self.cap.isOpened():
                    self.fps = self.cap.get(cv2.CAP_PROP_FPS)
                    self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    self.use_video = True
                    print(f"成功打开视频文件，FPS: {self.fps}")
                    print(f"分辨率: {self.width}x{self.height}")
                else:
                    print(f"无法打开视频文件 {self.video_path}，将使用模拟画面")
            except Exception as e:
                print(f"视频初始化错误: {e}，将使用模拟画面")
    
    def _load_fonts(self):
        """加载中文字体"""
        try:
            self._font_large = ImageFont.truetype("simhei.ttf", 24)
            self._font_small = ImageFont.truetype("simhei.ttf", 20)
        except:
            try:
                self._font_large = ImageFont.truetype("msyh.ttc", 24)
                self._font_small = ImageFont.truetype("msyh.ttc", 20)
            except:
                self._font_large = ImageFont.load_default()
                self._font_small = ImageFont.load_default()
    
    def get_frame(self) -> Optional[np.ndarray]:
        """获取当前帧"""
        if self.use_video and self.cap:
            ret, frame = self.cap.read()
            if ret:
                # 缩放到目标尺寸
                if frame.shape[1] != self.width or frame.shape[0] != self.height:
                    frame = cv2.resize(frame, (self.width, self.height))
                return frame
            else:
                # 对于RTMP流，尝试重新连接
                if self.video_path and self.video_path.startswith('rtmp://'):
                    print("RTMP流断开，尝试重新连接...")
                    self.cap.release()
                    time.sleep(1)  # 等待1秒后重连
                    self._initialize_video()
                    if self.use_video and self.cap:
                        ret, frame = self.cap.read()
                        if ret:
                            if frame.shape[1] != self.width or frame.shape[0] != self.height:
                                frame = cv2.resize(frame, (self.width, self.height))
                            return frame
                return None
        else:
            # 返回黑色背景
            frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
            cv2.putText(frame, "NO VIDEO SIGNAL", (50, self.height//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)
            return frame
    
    def render_trajectory(self, frame: np.ndarray, heading_angle: float) -> np.ndarray:
        """
        在帧上渲染轨迹线
        
        Args:
            frame: 输入图像帧
            heading_angle: 航向角（-180到180度）
            
        Returns:
            渲染后的图像帧
        """
        # 复制帧以避免修改原始数据
        result_frame = frame.copy()
        
        # 获取当前帧的尺寸
        frame_height, frame_width = frame.shape[:2]
        
        # 动态调整轨迹线参数
        if self.enable_dynamic_sizing:
            self._update_trajectory_params(frame_width, frame_height)
        
        # 绘制弯曲轨迹线对（红色）
        self._draw_curved_trajectory_pair(
            result_frame, 
            frame_width // 2,  # 使用实际帧宽度的中心
            frame_height - 50,  # 使用实际帧高度
            heading_angle,
            frame_width,
            frame_height
        )
        
        # 绘制白色轨迹线对
        self._draw_white_trajectory_pair(
            result_frame, 
            frame_width // 2,  # 使用实际帧宽度的中心
            frame_height - 50,  # 使用实际帧高度
            heading_angle,
            frame_width,
            frame_height
        )
        
        # 绘制信息文本
        self._draw_info(result_frame, heading_angle, frame_width, frame_height)
        
        return result_frame
    
    def _update_trajectory_params(self, frame_width: int, frame_height: int):
        """
        根据帧尺寸动态更新轨迹线参数
        
        Args:
            frame_width: 帧宽度
            frame_height: 帧高度
        """
        # 如果设置了手动参数，优先使用手动参数
        if self.manual_width is not None:
            self.bottom_width = self.manual_width
        else:
            # 轨迹线间距为视频宽度的比例
            self.bottom_width = int(frame_width * self.width_ratio)
        
        self.top_width = int(self.bottom_width * 0.7)
    
    def set_manual_params(self, length: Optional[int] = None, width: Optional[int] = None):
        """
        手动设置轨迹线参数
        
        Args:
            length: 手动设置的轨迹线长度（像素），None表示使用动态计算
            width: 手动设置的轨迹线宽度（像素），None表示使用动态计算
        """
        self.manual_length = length
        self.manual_width = width
    
    def set_dynamic_params(self, length_multiplier: float = None, width_ratio: float = None):
        """
        设置动态参数的倍数和比例
        
        Args:
            length_multiplier: 轨迹线长度为视频高度的倍数
            width_ratio: 轨迹线间距为视频宽度的比例
        """
        if length_multiplier is not None:
            self.length_multiplier = length_multiplier
        if width_ratio is not None:
            self.width_ratio = width_ratio
    
    def _draw_dashed_polyline(self, frame: np.ndarray, points: np.ndarray, color: tuple, thickness: int):
        """
        绘制虚线多边形
        
        Args:
            frame: 图像帧
            points: 点数组
            color: 颜色
            thickness: 线条粗细
        """
        if len(points) < 2:
            return
            
        total_distance = 0
        distances = []
        
        # 计算每段的距离
        for i in range(len(points) - 1):
            dist = np.linalg.norm(points[i+1] - points[i])
            distances.append(dist)
            total_distance += dist
        
        # 绘制虚线
        current_distance = 0
        dash_cycle = self.dash_length + self.gap_length
        
        for i in range(len(points) - 1):
            segment_start = points[i]
            segment_end = points[i + 1]
            segment_length = distances[i]
            
            if segment_length == 0:
                continue
                
            # 在当前线段上绘制虚线
            segment_distance = 0
            while segment_distance < segment_length:
                # 计算在整个虚线循环中的位置
                cycle_position = (current_distance + segment_distance) % dash_cycle
                
                # 判断是否在虚线段内
                if cycle_position < self.dash_length:
                    # 计算当前虚线段的起始和结束位置
                    dash_start_dist = segment_distance
                    dash_end_dist = min(segment_distance + (self.dash_length - cycle_position), segment_length)
                    
                    # 计算实际坐标
                    start_ratio = dash_start_dist / segment_length
                    end_ratio = dash_end_dist / segment_length
                    
                    dash_start = segment_start + start_ratio * (segment_end - segment_start)
                    dash_end = segment_start + end_ratio * (segment_end - segment_start)
                    
                    # 绘制虚线段
                    cv2.line(frame, 
                            (int(dash_start[0]), int(dash_start[1])), 
                            (int(dash_end[0]), int(dash_end[1])), 
                            color, thickness)
                    
                    segment_distance = dash_end_dist
                else:
                    # 跳过间隔部分
                    skip_distance = min(dash_cycle - cycle_position, segment_length - segment_distance)
                    segment_distance += skip_distance
            
            current_distance += segment_length
    
    def _draw_curved_trajectory_pair(self, frame: np.ndarray, center_x: int, start_y: int, heading_angle: float, frame_width: int, frame_height: int):
        """绘制一对弯曲的轨迹线（左右两条）"""
        left_points = []
        right_points = []
        
        # 根据航向角计算弯曲程度
        curve_factor = heading_angle / 180.0  # 归一化到-1到1之间
        
        # 计算目标Y坐标范围（从底部到顶部延伸）
        target_top_y = -int(frame_height * 0.5)  # 延伸到屏幕顶部之外
        y_step = 10  # Y方向固定步长
        
        # 从起始Y坐标向上绘制到目标顶部
        current_y = start_y
        step_count = 0
        total_y_distance = start_y - target_top_y
        
        while current_y > target_top_y:
            # 计算Y方向的进度（0到1）
            y_progress = (start_y - current_y) / total_y_distance if total_y_distance > 0 else 0
            y_progress = max(0, min(1, y_progress))  # 确保在0-1范围内
            
            # 根据进度计算当前轨迹线间距（上窄下宽）
            current_width = self.bottom_width * (1 - y_progress) + self.top_width * y_progress
            half_width = current_width / 2
            
            # 根据航向角和进度计算X方向的偏移
            # 使用更平滑的弯曲算法，类似倒车影像
            # 使用二次函数创建更自然的弯曲效果
            curve_intensity = curve_factor * frame_width * 0.4  # 弯曲强度
            # 二次弯曲：在远处弯曲更明显
            x_offset = curve_intensity * (y_progress ** 1.5)  # 使用1.5次方创建渐进弯曲
            center_x_current = center_x + x_offset
            
            # 计算左右轨迹点
            left_x = int(center_x_current - half_width)
            left_y = int(current_y)
            right_x = int(center_x_current + half_width)
            right_y = int(current_y)
            
            left_points.append((left_x, left_y))
            right_points.append((right_x, right_y))
            
            # 向上移动固定步长
            current_y -= y_step
            step_count += 1
        
        # 绘制左右轨迹线（虚线效果）
        color = (0, 0, 255)  # 红色
        thickness = 4
        
        if len(left_points) > 1:
            left_points_array = np.array(left_points, dtype=np.float32)
            self._draw_dashed_polyline(frame, left_points_array, color, thickness)
        
        if len(right_points) > 1:
            right_points_array = np.array(right_points, dtype=np.float32)
            self._draw_dashed_polyline(frame, right_points_array, color, thickness)
    
    def _draw_white_trajectory_pair(self, frame: np.ndarray, center_x: int, start_y: int, heading_angle: float, frame_width: int, frame_height: int):
        """绘制一对白色弯曲轨迹线（左右两条），间距为80%视频宽度"""
        left_points = []
        right_points = []
        
        # 根据航向角计算弯曲程度
        curve_factor = heading_angle / 180.0  # 归一化到-1到1之间
        
        # 白色轨迹线的间距为80%视频宽度
        white_bottom_width = int(frame_width * 0.8)
        white_top_width = int(white_bottom_width * 0.7)  # 顶部宽度为底部宽度的80%
        
        # 计算目标Y坐标范围（从底部到顶部延伸）
        target_top_y = -int(frame_height * 0.5)  # 延伸到屏幕顶部之外
        y_step = 10  # Y方向固定步长
        
        # 从起始Y坐标向上绘制到目标顶部
        current_y = start_y
        step_count = 0
        total_y_distance = start_y - target_top_y
        
        while current_y > target_top_y:
            # 计算Y方向的进度（0到1）
            y_progress = (start_y - current_y) / total_y_distance if total_y_distance > 0 else 0
            y_progress = max(0, min(1, y_progress))  # 确保在0-1范围内
            
            # 根据进度计算当前轨迹线间距（上窄下宽）
            current_width = white_bottom_width * (1 - y_progress) + white_top_width * y_progress
            half_width = current_width / 2
            
            # 根据航向角和进度计算X方向的偏移
            # 使用更平滑的弯曲算法，类似倒车影像
            # 使用二次函数创建更自然的弯曲效果
            curve_intensity = curve_factor * frame_width * 0.4  # 弯曲强度
            # 二次弯曲：在远处弯曲更明显
            x_offset = curve_intensity * (y_progress ** 1.5)  # 使用1.5次方创建渐进弯曲
            center_x_current = center_x + x_offset
            
            # 计算左右轨迹点
            left_x = int(center_x_current - half_width)
            left_y = int(current_y)
            right_x = int(center_x_current + half_width)
            right_y = int(current_y)
            
            left_points.append((left_x, left_y))
            right_points.append((right_x, right_y))
            
            # 向上移动固定步长
            current_y -= y_step
            step_count += 1
        
        # 绘制左右白色轨迹线（虚线效果）
        color = (255, 255, 255)  # 白色
        thickness = 4
        
        if len(left_points) > 1:
            left_points_array = np.array(left_points, dtype=np.float32)
            self._draw_dashed_polyline(frame, left_points_array, color, thickness)
        
        if len(right_points) > 1:
            right_points_array = np.array(right_points, dtype=np.float32)
            self._draw_dashed_polyline(frame, right_points_array, color, thickness)
    
    def _draw_info(self, frame: np.ndarray, heading_angle: float, frame_width: int, frame_height: int):
        """在画面上显示信息"""
        # 将OpenCV图像转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 显示航向角信息
        heading_text = f"航向角: {heading_angle:.1f}°"
        draw.text((frame_width - 200, 10), heading_text, font=self._font_large, fill=(255, 255, 255))
        
        
        # 将PIL图像转换回OpenCV格式
        frame[:] = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    def get_trajectory_boundaries(self, center_x: int, start_y: int, heading_angle: float, frame_width: int, frame_height: int, target_y: int):
        """
        计算指定Y坐标处的轨迹线边界位置
        
        Args:
            center_x: 轨迹线中心X坐标
            start_y: 轨迹线起始Y坐标
            heading_angle: 航向角
            frame_width: 帧宽度
            frame_height: 帧高度
            target_y: 目标Y坐标
            
        Returns:
            dict: 包含红色和白色轨迹线边界的字典
                {
                    'red_left': int,
                    'red_right': int,
                    'white_left': int,
                    'white_right': int
                }
        """
        # 初始方向向上
        current_angle = -90  # 向上为-90度
        
        # 根据航向角计算弯曲程度
        curve_factor = heading_angle / 180.0  # 归一化到-1到1之间
        
        # 动态计算轨迹线长度
        if self.manual_length is not None:
            total_length = self.manual_length
        else:
            total_length = int(frame_height * self.length_multiplier)
        
        # 轨迹线长度和步长
        step_size = 5
        steps = int(total_length / step_size)
        
        # 更新轨迹线参数
        if self.enable_dynamic_sizing:
            self._update_trajectory_params(frame_width, frame_height)
        
        # 白色轨迹线的间距为80%视频宽度
        white_bottom_width = int(frame_width * 0.8)
        white_top_width = int(white_bottom_width * 0.8)
        
        # 中心线起点
        center_x_current = center_x
        center_y_current = start_y
        
        # 寻找最接近target_y的位置
        closest_distance = float('inf')
        red_left_x = red_right_x = white_left_x = white_right_x = center_x
        
        for i in range(steps):
            # 计算距离进度（0到1）
            progress = i / steps
            
            # 根据航向角逐渐调整方向
            angle_change = curve_factor * progress * 90  # 最大90度的弯曲
            current_angle_rad = math.radians(current_angle + angle_change)
            
            # 计算垂直于前进方向的向量（用于左右偏移）
            perpendicular_angle = current_angle_rad + math.pi / 2
            
            # 检查是否接近目标Y坐标
            distance = abs(center_y_current - target_y)
            if distance < closest_distance:
                closest_distance = distance
                
                # 计算红色轨迹线边界
                red_current_width = self.bottom_width * (1 - progress) + self.top_width * progress
                red_half_width = red_current_width / 2
                red_left_x = int(center_x_current - red_half_width * math.cos(perpendicular_angle))
                red_right_x = int(center_x_current + red_half_width * math.cos(perpendicular_angle))
                
                # 计算白色轨迹线边界
                white_current_width = white_bottom_width * (1 - progress) + white_top_width * progress
                white_half_width = white_current_width / 2
                white_left_x = int(center_x_current - white_half_width * math.cos(perpendicular_angle))
                white_right_x = int(center_x_current + white_half_width * math.cos(perpendicular_angle))
            
            # 更新中心线位置
            center_x_current += step_size * math.cos(current_angle_rad)
            center_y_current += step_size * math.sin(current_angle_rad)
            
            # 如果超出屏幕顶部，停止绘制
            if center_y_current < -50:
                break
        
        return {
            'red_left': red_left_x,
            'red_right': red_right_x,
            'white_left': white_left_x,
            'white_right': white_right_x
        }
    
    def set_trajectory_width(self, bottom_width: int, top_width: int):
        """设置轨迹线间距参数"""
        self.bottom_width = bottom_width
        self.top_width = top_width
    
    def get_video_info(self) -> dict:
        """获取视频信息"""
        return {
            'width': self.width,
            'height': self.height,
            'fps': self.fps,
            'use_video': self.use_video,
            'video_path': self.video_path
        }
    
    def release(self):
        """释放资源"""
        if self.cap:
            self.cap.release()


class TrajectoryController:
    """基于航向角的轨迹线控制器 - 包含回正功能"""
    
    def __init__(self):
        # 航向角控制
        self.current_heading = 0.0  # 当前航向角
        self.prev_heading = 0.0  # 上一次的航向角
        self.last_heading_change_time = time.time()  # 上次航向角变化的时间
        self.heading_return_speed = 90.0  # 轨迹线回正速度（度/秒）
        self.heading_change_threshold = 0.5  # 航向角变化检测阈值（秒）
        
        # 键盘控制
        self.key_control_speed = 5  # 键盘控制时的角度变化速度
    
    def update_heading(self, heading_angle):
        """更新航向角并记录变化时间"""
        # 检测航向角是否发生变化
        if abs(heading_angle - self.current_heading) > 0.1:  # 小阈值，避免浮点误差
            # 更新航向角记录
            self.prev_heading = self.current_heading
            self.current_heading = heading_angle
            self.last_heading_change_time = time.time()
            
            # 限制航向角在-180到180度之间
            self.current_heading = max(-180, min(180, self.current_heading))
    
    def update(self, dt):
        """更新轨迹线状态 - 处理自动回正"""
        current_time = time.time()
        
        # 检查航向角是否长时间未变化，如果是则让航向角归零
        if current_time - self.last_heading_change_time > self.heading_change_threshold:
            # 如果当前航向角不为0，缓慢归零
            if abs(self.current_heading) > 0.1:
                # 计算这一帧应该减少的角度
                heading_change = self.heading_return_speed * dt
                
                # 航向角缓慢归零
                if self.current_heading > 0:
                    self.current_heading = max(0, self.current_heading - heading_change)
                else:
                    self.current_heading = min(0, self.current_heading + heading_change)
    
    def get_current_heading(self):
        """获取当前航向角"""
        return self.current_heading
    
    def set_heading(self, heading_angle):
        """直接设置航向角"""
        self.update_heading(heading_angle)


class TrajectoryViewer:
    """轨迹线查看器 - 提供交互式界面"""
    
    def __init__(self, video_path: Optional[str] = None, bottom_width: int = 200, top_width: int = 50, heading_callback=None, enable_auto_return: bool = True):
        """
        初始化轨迹线查看器
        
        Args:
            video_path: 视频文件路径
            bottom_width: 轨迹线底部间距
            top_width: 轨迹线顶部间距
            heading_callback: 航向角回调函数，返回当前航向角值
            enable_auto_return: 是否启用自动回正功能
        """
        self.renderer = TrajectoryRenderer(video_path, bottom_width, top_width)
        self.controller = TrajectoryController()
        self.running = False
        self.heading_callback = heading_callback
        self.enable_auto_return = enable_auto_return
        self.last_time = time.time()
    
    def set_heading_callback(self, callback):
        """设置航向角回调函数
        
        Args:
            callback: 回调函数，应该返回当前的航向角值 (float)
                     例如: lambda: get_current_heading_from_sensor()
        """
        self.heading_callback = callback
    
    def run(self, use_keyboard_control: bool = True):
        """运行交互式查看器
        
        Args:
            use_keyboard_control: 是否使用键盘控制，False时完全依赖回调函数
        """
        cv2.namedWindow("Trajectory Viewer", cv2.WINDOW_NORMAL)
        self.running = True
        
        print("轨迹线查看器启动")
        if use_keyboard_control:
            print("控制方式:")
            print("  航向角由外部系统提供")
            print("  按Q键退出")
        else:
            print("使用回调函数模式，Q键退出")
        
        if self.enable_auto_return:
            print("自动回正功能已启用")
        
        while self.running:
            # 计算时间差
            current_time = time.time()
            dt = current_time - self.last_time
            self.last_time = current_time
            
            # 处理键盘输入
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q') or cv2.getWindowProperty("Trajectory Viewer", cv2.WND_PROP_VISIBLE) < 1:
                break
            
            key_pressed = False
            # A/D键控制已移除，航向角由外部系统提供
            
            # 如果有回调函数，优先使用回调函数获取航向角
            if self.heading_callback is not None:
                try:
                    new_heading = self.heading_callback()
                    if isinstance(new_heading, (int, float)):
                        self.controller.update_heading(float(new_heading))
                except Exception as e:
                    print(f"航向角回调函数执行错误: {e}")
            
            # 只有在没有按键输入且启用自动回正时才执行自动回正
            if not key_pressed and self.enable_auto_return:
                self.controller.update(dt)
            
            # 获取帧
            frame = self.renderer.get_frame()
            if frame is None:
                print("视频播放结束")
                break
            
            # 渲染轨迹线
            result_frame = self.renderer.render_trajectory(frame, self.controller.get_current_heading())
            
            # 显示
            cv2.imshow("Trajectory Viewer", result_frame)
        
        self.stop()
    
    def stop(self):
        """停止查看器"""
        self.running = False
        self.renderer.release()
        cv2.destroyAllWindows()
    
    def set_heading(self, heading_angle: float):
        """设置航向角"""
        self.controller.set_heading(heading_angle)
    
    def get_heading(self) -> float:
        """获取当前航向角"""
        return self.controller.get_current_heading()
    
    def set_auto_return(self, enable: bool):
        """设置是否启用自动回正功能"""
        self.enable_auto_return = enable
    
    def set_return_speed(self, speed: float):
        """设置回正速度（度/秒）"""
        self.controller.heading_return_speed = speed
    
    def set_return_threshold(self, threshold: float):
        """设置回正触发阈值（秒）"""
        self.controller.heading_change_threshold = threshold


# 便捷函数
def create_trajectory_renderer(video_path: Optional[str] = None, 
                             bottom_width: int = 200, 
                             top_width: int = 50) -> TrajectoryRenderer:
    """创建轨迹线渲染器"""
    return TrajectoryRenderer(video_path, bottom_width, top_width)


def render_single_frame(heading_angle: float, 
                       video_path: Optional[str] = None,
                       bottom_width: int = 200,
                       top_width: int = 50) -> Optional[np.ndarray]:
    """渲染单帧图像"""
    renderer = TrajectoryRenderer(video_path, bottom_width, top_width)
    frame = renderer.get_frame()
    if frame is not None:
        result = renderer.render_trajectory(frame, heading_angle)
        renderer.release()
        return result
    renderer.release()
    return None


def start_interactive_viewer(video_path: Optional[str] = None,
                            bottom_width: int = 200,
                            top_width: int = 50,
                            heading_callback=None,
                            use_keyboard_control: bool = True,
                            enable_auto_return: bool = True,
                            return_speed: float = 90.0,
                            return_threshold: float = 0.5):
    """启动交互式查看器
    
    Args:
        video_path: 视频文件路径
        bottom_width: 轨迹线底部间距
        top_width: 轨迹线顶部间距
        heading_callback: 航向角回调函数，返回当前航向角值
        use_keyboard_control: 是否启用键盘控制
        enable_auto_return: 是否启用自动回正功能
        return_speed: 回正速度（度/秒）
        return_threshold: 回正触发阈值（秒）
    """
    viewer = TrajectoryViewer(video_path, bottom_width, top_width, heading_callback, enable_auto_return)
    viewer.set_return_speed(return_speed)
    viewer.set_return_threshold(return_threshold)
    viewer.run(use_keyboard_control)


if __name__ == "__main__":
    # 示例用法
    video_path = "G:/0716/1.mp4"  # 可以设置为None使用黑色背景
    
    # 方法1: 使用交互式查看器 - 键盘控制模式（带自动回正）
    # start_interactive_viewer(
    #     video_path=video_path, 
    #     bottom_width=2000, 
    #     top_width=1200,
    #     enable_auto_return=True,  # 启用自动回正
    #     return_speed=90.0,        # 回正速度90度/秒
    #     return_threshold=0.5      # 0.5秒后开始回正
    # )
    
    # 方法2: 回调函数模式示例（带自动回正）
    import math
    
    # 模拟传感器数据的回调函数
    def simulate_heading_sensor():
        """模拟航向角传感器数据"""
        # 这里可以替换为实际的传感器读取代码
        # 例如: return get_imu_heading() 或 return get_gps_heading()
        return math.sin(time.time() * 0.5) * 90  # 模拟-90到90度的正弦波变化
    
    rtmp_url = "G:/0716/1.mp4"
    # 使用回调函数启动查看器
    start_interactive_viewer(
        # video_path=rtmp_url,
        video_path=video_path, 
        bottom_width=2000, 
        top_width=1200,
        # heading_callback=simulate_heading_sensor,
        use_keyboard_control=True,  # 启用键盘控制
        enable_auto_return=True,    # 启用自动回正功能
        return_speed=90.0,          # 回正速度
        return_threshold=0.5        # 回正触发阈值
    )
    
    # 方法3: 禁用自动回正的模式
    # start_interactive_viewer(
    #     video_path=video_path,
    #     bottom_width=2000,
    #     top_width=1200,
    #     enable_auto_return=False  # 禁用自动回正
    # )
    
    # 方法4: 使用渲染器API
    # renderer = create_trajectory_renderer(video_path, 200, 50)
    # frame = renderer.get_frame()
    # if frame is not None:
    #     result = renderer.render_trajectory(frame, 45.0)  # 45度航向角
    #     cv2.imshow("Result", result)
    #     cv2.waitKey(0)
    # renderer.release()
    # cv2.destroyAllWindows()