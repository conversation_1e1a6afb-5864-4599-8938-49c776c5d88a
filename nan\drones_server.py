import sys
import time
import traceback
import uuid
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
import math

from nan.logger_config import setup_logging

logger = setup_logging()

def login_token_get():
    #登录获取token
    url = nan.constants.baseURL + nan.constants.loginAddr
    data = {"username": nan.constants.loginInfo.username, "password": nan.constants.loginInfo.password, "captcha": nan.constants.loginInfo.captcha, "checkKey": nan.constants.loginInfo.checkKey}
    
    headers = {"Content-Type": "application/json"}
    params = {}
    print(f"{data}")
    try:
        token = post_requests_response(url,data,headers,params).get("result", {}).get("token", "")
        token_request = "Bearer "+token
        nan.constants.constant_token = token_request
        logger.info(f"login_token_get######获取到的token：{token}")
    except Exception as e:
        logger.error(f"login_token_get######获取token错误：{e}")
        token = ""
        token_request = "Bearer "+token
        nan.constants.constant_token = token_request
        
        traceback.print_exc()  
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
        
        
def active_drones_get():
    #获取正在飞行任务中的无人机列表
    url = nan.constants.baseURL + nan.constants.activeAddr
    data = {}
    headers = {"Authorization": nan.constants.constant_token}
    params = {}

    try:
        response = get_requests_response(url,data,headers,params)
        logger.info(f"获取正在飞行任务中的无人机列表：{response}")
    except Exception as e:
        logger.error(f"获取正在飞行任务中的无人机列表错误：{e}")
        active_drones_list = []
        traceback.print_exc()  
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
    active_drones_list = response.get("result", {})
    return active_drones_list

def drone_state_get(active_drone):
    #获取无人机实时状态信息
    droneDeviceSn = ""
    if "droneDeviceSn" in active_drone:
        droneDeviceSn = active_drone.get("droneDeviceSn")
    else:
        droneDeviceSn = ""
    url = nan.constants.baseURL + nan.constants.stateAddr
    data = {}
    headers = {"Authorization": nan.constants.constant_token}
    params = {"sn":droneDeviceSn}
    try:
        drone_state = get_requests_response(url,data,headers,params) 
        #drone_state = {"success":True,"message":"","code":200,"result":{"81-0-0":{"gimbal_pitch":0,"gimbal_roll":0,"gimbal_yaw":166.0488721363072,"payload_index":"81-0-0","thermal_current_palette_style":0,"thermal_gain_mode":2,"thermal_global_temperature_max":45.27458953857422,"thermal_global_temperature_min":-6.783911228179932,"thermal_isotherm_lower_limit":-20,"thermal_isotherm_state":0,"thermal_isotherm_upper_limit":150,"zoom_factor":0.5678233438485805},"activation_time":1743442161,"attitude_head":-166,"attitude_pitch":-1.6,"attitude_roll":-0.1,"battery":{"batteries":[{"capacity_percent":87,"firmware_version":"26.03.00.37","high_voltage_storage_days":5,"index":0,"loop_times":54,"sn":"6Q7PN28DA100VV","sub_type":0,"temperature":31.9,"type":0,"voltage":16265}],"capacity_percent":87,"landing_power":0,"remain_flight_time":0,"return_home_power":0},"best_link_gateway":"7CTXMBL00B04HF","cameras":[{"camera_mode":1,"ir_metering_mode":0,"ir_metering_point":{"temperature":0,"x":0.5,"y":0.5},"ir_zoom_factor":2,"liveview_world_region":{"bottom":0.5639163255691528,"left":0.43149545788764954,"right":0.5648463368415833,"top":0.4346466064453125},"payload_index":"81-0-0","photo_state":0,"record_time":0,"recording_state":0,"remain_photo_num":0,"remain_record_duration":5567,"screen_split_enable":False,"video_storage_settings":["ir","vision"],"wide_calibrate_farthest_focus_value":34,"wide_calibrate_nearest_focus_value":64,"wide_exposure_mode":1,"wide_exposure_value":16,"wide_focus_mode":2,"wide_focus_state":0,"wide_focus_value":64,"wide_iso":4,"wide_max_focus_value":64,"wide_min_focus_value":33,"wide_shutter_speed":3,"zoom_calibrate_farthest_focus_value":34,"zoom_calibrate_nearest_focus_value":64,"zoom_exposure_mode":1,"zoom_exposure_value":16,"zoom_factor":6.999994214380596,"zoom_focus_mode":2,"zoom_focus_state":0,"zoom_focus_value":64,"zoom_iso":4,"zoom_max_focus_value":64,"zoom_min_focus_value":33,"zoom_shutter_speed":3}],"country":"CN","distance_limit_status":{"distance_limit":5000,"is_near_distance_limit":0,"state":0},"elevation":0,"gear":1,"height":1.2136611938476562,"height_limit":230,"home_distance":0.04931320250034332,"horizontal_speed":0,"is_near_area_limit":0,"is_near_height_limit":0,"latitude":39.07800737831422,"longitude":116.17892704468109,"maintain_status":{"maintain_status_array":[{"last_maintain_flight_sorties":0,"last_maintain_flight_time":0,"last_maintain_time":0,"last_maintain_type":1,"state":0},{"last_maintain_flight_sorties":0,"last_maintain_flight_time":0,"last_maintain_time":0,"last_maintain_type":2,"state":0},{"last_maintain_flight_sorties":0,"last_maintain_flight_time":0,"last_maintain_time":0,"last_maintain_type":3,"state":0}]},"mode_code":0,"night_lights_state":0,"obstacle_avoidance":{"downside":1,"horizon":1,"upside":1},"position_state":{"gps_number":31,"is_fixed":2,"quality":5,"rtk_number":36},"rc_lost_action":2,"rid_state":True,"rth_altitude":60,"storage":{"total":60397000,"used":2000},"total_flight_distance":314810.1611482897,"total_flight_sorties":236,"total_flight_time":76702.09828557447,"track_id":"","vertical_speed":0,"wind_direction":0,"wind_speed":0,"device_sn":"1581F6Q8X24BM00G016L","timestamp":1749540602033},"timestamp":1749540601846}
        return drone_state
    except Exception as e:
        drone_state = {}
        logger.error(f"获取无人机实时状态信息错误：{e}")
        traceback.print_exc()  
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
        return drone_state


def alarm_info_post(active_drone,drone_state,classes,alarmLevel,alarmImageUrl,videoUrl):

    """ai报警接口参数

    Args:
        classes        (String):检测目标类别 
        alarmLevel     (int):报警等级
        alarmImageUrl  (String):报警图片在minio中的地址
        videoUrl       (String):报警前后5秒视频在minio中的地址
    Returns:
    """
    url = nan.constants.baseURL + nan.constants.alarmAddr
    data = {}
    headers = {"Content-Type": "application/json","Authorization":nan.constants.constant_token}
    params = {}
    
    try:
        # 获取无人机经纬度坐标
        print(f"ai报警上报当前无人机状态：{drone_state}")
        logger.info(f"ai报警上报当前无人机状态：{drone_state}")
        alarmAccuratePosition = ""
        if "result" in drone_state:
            if "longitude" in drone_state.get("result"):
                alarmAccuratePosition = alarmAccuratePosition + str(drone_state.get("result").get("longitude"))
            alarmAccuratePosition = alarmAccuratePosition + "," 
            if "latitude" in drone_state.get("result"):
                alarmAccuratePosition = alarmAccuratePosition + str(drone_state.get("result").get("latitude"))
        
        data["deviceType"]            = nan.constants.alarmInfo.deviceType
        data["aiAlgorithmVendorId"]   = nan.constants.alarmInfo.aiAlgorithmVendorId
        data["alarmId"]               = str(uuid.uuid4())
        data["taskId"]                = active_drone.get("taskId")
        data["gatewayDeviceSn"]       = active_drone.get("gatewayDeviceSn")
        data["droneDeviceSn"]         = active_drone.get("droneDeviceSn")
        data["classes"]               = classes
        data["taskType"]              = nan.constants.alarmInfo.taskType
        data["alarmLevel"]            = alarmLevel
        data["monitorEq"]             = active_drone.get("monitorEq") 
        data["area"]                  = active_drone.get("area")
        data["alarmLine"]             = active_drone.get("rootName")
        data["alarmTimestamp"]        = int(time.time() * 1000)
        data["alarmImageUrl"]         = alarmImageUrl
        data["videoUrl"]              = videoUrl
        data["alarmAccuratePosition"] = alarmAccuratePosition
        
        print(f"ai报警上报请求内容：{data}")
        logger.info(f"ai报警上报请求内容：{data}")
        respond = post_requests_response(url,data,headers,params)
        logger.info(f"ai报警上报收到的响应：{respond}")
    except Exception as e:
        logger.error(f"ai报警错误：{e}")
        traceback.print_exc()
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")



def find_gimbal_pitch(data,input_key):
    results = []
    if isinstance(data, dict):
        if input_key in data:
            results.append(data[input_key])
        for key, value in data.items():
            results.extend(find_gimbal_pitch(value,input_key))
    return results

def drone_yaw_get(active_drone):
    yaw_diff = 0
    key = nan.constants.yawInfo.key_yaw
    drone_state = drone_state_get(active_drone)
    #logger.info(f"当前无人机状态：{drone_state}")
    #print(f"当前无人机状态：{drone_state}")
    try:
        gimbal_pitch_results = find_gimbal_pitch(drone_state,key)
        gimbal_yaw = gimbal_pitch_results[0]
        yaw_diff = calculate_rotation_diff(nan.constants.yawInfo.his_yaw,gimbal_yaw)
        yaw_diff = yaw_diff * 85 / 360
        nan.constants.yawInfo.his_yaw = gimbal_yaw
        
    except Exception as e:
        logger.error(f"航向角获取错误：{e}")
        logger.error(f"无人机状态：{drone_state}")
        traceback.print_exc()
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
        yaw_diff = 0
    return yaw_diff
    
    
def calculate_rotation_diff(angle_a, angle_b):
    # 计算原始角度差
    raw_diff = angle_b - angle_a
    
    # 规范化差值到 [-180, 180] 范围
    normalized_diff = (raw_diff + 180) % 360 - 180
    
    # 进一步约束到 [-180, 180]
    if normalized_diff < -180:
        normalized_diff += 360
    elif normalized_diff > 180:
        normalized_diff -= 360
    
    return normalized_diff