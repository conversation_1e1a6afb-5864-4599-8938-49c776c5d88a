# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS

修复推流视频检测边框闪烁问题的优化版本
主要修改：
1. 统一推流队列管理，避免帧类型混合
2. 增强结果缓存机制，确保检测框连续显示
3. 优化推流逻辑，避免在原始帧和检测帧之间切换
4. 改为同步推理，确保结果及时性
"""

import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque
from enum import Enum

from ultralytics import YOLO
import subprocess
from ultralytics.utils.plotting import Annotator, colors, save_one_box
import logging
import json
import torch

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math
import sys
from apscheduler.schedulers.blocking import BlockingScheduler

logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class AlgorithmMode(Enum):
    """算法模式枚举"""
    TRACKING_ONLY = "tracking_only"  # 仅目标追踪
    SEGMENTATION_ONLY = "segmentation_only"  # 仅分割
    HYBRID = "hybrid"  # 混合模式（同时运行两种算法）

class HybridConfig:
    """混合算法配置类 - 优化版本，解决闪烁问题"""
    def __init__(self):
        # 算法模式配置
        self.algorithm_mode = AlgorithmMode.HYBRID  # 默认混合模式
        
        # 模型路径配置
        self.tracking_model_path = "/home/<USER>/suanfa/czy/vis-m-bs16-sz960.pt"
        self.segmentation_model_path = "/home/<USER>/suanfa/czy/water.pt"
        
        # 类别配置
        self.tracking_classes = ["None", "people", "bicycle", "car", "van", "truck", "tricycle", "awning-tricycle", "bus", "motor"]
        self.segmentation_classes = ["water"]
        
        # 性能配置 - 优化推理间隔，确保检测结果连续性
        self.tracking_conf_threshold = 0.7
        self.segmentation_conf_threshold = 0.5
        self.tracking_inference_interval = 1  # 追踪算法推理间隔（帧数）- 每帧推理，确保连续性
        self.segmentation_inference_interval = 2  # 分割算法推理间隔（帧数）- 每2帧推理一次
        
        # 异步执行配置 - 禁用异步推理，避免结果延迟
        self.enable_async_inference = False  # 禁用异步推理，避免结果延迟导致闪烁
        self.max_inference_threads = 2  # 最大推理线程数
        
        # 结果融合配置
        self.enable_result_fusion = True  # 启用结果融合
        self.fusion_alpha = 0.7  # 融合透明度
        
        # 推流优化配置 - 确保检测结果连续显示
        self.prioritize_original_stream = False  # 始终优先显示检测结果
        self.detection_overlay_ratio = 1.0  # 检测结果叠加比例（0.0-1.0）- 设为1.0显示所有检测框
        self.stream_quality_priority = False  # 优先显示检测结果而非推流质量
        
        # 智能负载均衡配置
        self.adaptive_quality = True  # 启用自适应质量调整
        self.max_targets_threshold = 15  # 目标数量阈值，超过此数量时启用性能优化
        self.performance_mode = False  # 性能模式标志
        
        # 告警控制参数
        self.alarm_target_threshold = 2  # 告警目标数量阈值，当追踪目标数量超过此阈值时才触发告警
        self.alarm_interval_seconds = 2.0  # 告警间隔时间（秒），避免重复告警
        
        # 结果缓存配置 - 新增，解决闪烁问题
        self.max_result_valid_frames = 30  # 结果最大有效帧数，确保检测框持续显示
        self.display_frame_timeout = 1.0  # 显示帧超时时间（秒）
        
    def is_tracking_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.TRACKING_ONLY, AlgorithmMode.HYBRID]
    
    def is_segmentation_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.SEGMENTATION_ONLY, AlgorithmMode.HYBRID]

# 关键修改说明：
# 1. 禁用异步推理 (enable_async_inference = False)
# 2. 减少推理间隔 (tracking_inference_interval = 1)
# 3. 增加结果缓存时间 (max_result_valid_frames = 30)
# 4. 添加显示帧超时配置 (display_frame_timeout = 1.0)

class HybridMultiDealImg(object):
    """融合目标追踪和分割的混合检测类 - 优化版本，解决闪烁问题"""
    tracking_model = None  # 追踪模型类变量
    segmentation_model = None  # 分割模型类变量
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, config: HybridConfig, save_path, dict_json):
        self.config = config
        
        # 从配置中获取类别信息
        self.tracking_classes = config.tracking_classes
        self.segmentation_classes = config.segmentation_classes
        
        # 初始化模型
        if self.config.is_tracking_enabled() and HybridMultiDealImg.tracking_model is None:
            HybridMultiDealImg.tracking_model = YOLO(self.config.tracking_model_path).to("cuda")
        if self.config.is_segmentation_enabled() and HybridMultiDealImg.segmentation_model is None:
            HybridMultiDealImg.segmentation_model = YOLO(self.config.segmentation_model_path).to("cuda")
        
        self.tracking_model = HybridMultiDealImg.tracking_model
        self.segmentation_model = HybridMultiDealImg.segmentation_model
        
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化队列设计 - 统一推流队列管理，避免帧类型混合
        self.save_queue = queue.Queue(maxsize=15)  # 保存队列
        self.push_queue = queue.Queue(maxsize=15)  # 统一推流队列，只存放最终显示帧
        self.frame_queue = queue.Queue(maxsize=5)  # 帧队列

        # 结果存储 - 增强结果缓存机制，确保检测框持续显示
        self.tracking_results = None
        self.segmentation_results = None
        self.last_valid_tracking_results = None  # 最后有效的追踪结果
        self.last_valid_segmentation_results = None  # 最后有效的分割结果
        self.tracking_result_valid_frames = 0  # 追踪结果有效帧计数
        self.segmentation_result_valid_frames = 0  # 分割结果有效帧计数
        
        # 新增：最后显示帧缓存，确保推流连续性
        self.last_display_frame = None
        self.last_display_frame_time = 0
        
        self.dataT = 2
        
        # 其他初始化代码保持不变...
        # (这里省略了其他初始化代码，实际使用时需要从原文件复制)
