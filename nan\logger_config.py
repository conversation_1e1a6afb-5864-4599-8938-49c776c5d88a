import logging
from logging.handlers import TimedRotatingFileHandler
import os
from datetime import datetime

def setup_logging():
    # 1. 创建日志目录（如果不存在）
    log_dir = "log"
    os.makedirs(log_dir, exist_ok=True)  # 自动创建目录[2,9](@ref)

    # 2. 生成带日期的日志文件名（格式：app_年-月-日.log）
    log_filename = f"drones_{datetime.now().strftime('%Y-%m-%d')}.log"
    log_filepath = os.path.join(log_dir, log_filename)  # 拼接完整路径[2,10](@ref)

    # 3. 配置日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)  # 设置全局日志级别

    # 4. 创建按天分割的处理器（每天午夜生成新文件）
    handler = TimedRotatingFileHandler(
        filename=log_filepath,
        when="midnight",  # 每天午夜切割[6,9](@ref)
        interval=1,        # 每天一次
        backupCount=7,     # 保留最近7天的日志[6,9](@ref)
        encoding="utf-8"
    )

    # 5. 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)

    # 6. 添加处理器到日志记录器
    logger.addHandler(handler)

    return logger

# 使用示例
if __name__ == "__main__":
    logger = setup_logging()
    logger.info("程序启动")  # 日志会自动写入 log/app_2025-06-17.log
    logger.error("测试错误信息")