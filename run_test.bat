@echo off
echo ===============================
echo 性能测试和优化验证脚本
echo ===============================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo 当前目录: %cd%
echo.

REM 检查是否存在测试视频
if not exist "test_video.mp4" (
    echo 警告: 未找到测试视频文件 test_video.mp4
    echo 请将测试视频文件重命名为 test_video.mp4 并放置在当前目录
    echo 或者在脚本中修改视频路径
    echo.
    echo 可用的视频文件:
    dir *.mp4 2>nul
    echo.
    set /p video_name="请输入视频文件名（包含扩展名）或按回车使用默认: "
    if not "!video_name!"=="" (
        if exist "!video_name!" (
            copy "!video_name!" "test_video.mp4" >nul
            echo 已复制 !video_name! 为测试视频
        ) else (
            echo 错误: 文件 !video_name! 不存在
            pause
            exit /b 1
        )
    )
)

echo ===============================
echo 选择测试类型:
echo 1. 运行简单性能测试（不需要模型）
echo 2. 运行完整性能对比测试
echo 3. 视频连续性测试（诊断回闪问题）
echo 4. 查看优化建议
echo ===============================
set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 正在运行简单性能测试...
    echo 这将测试基本的视频处理性能，不需要YOLO模型
    echo 按 Ctrl+C 可以随时停止测试
    echo.
    pause
    python test_performance.py
) else if "%choice%"=="2" (
    echo.
    echo 正在运行完整性能对比测试...
    echo 这将比较原版本和优化版本的性能差异
    echo 测试时长: 30秒 x 2 = 60秒
    echo.
    pause
    python performance_comparison.py
) else if "%choice%"=="3" (
    echo.
    echo 正在运行视频连续性测试...
    echo 这将测试不同队列配置对视频流畅性的影响
    echo 可以帮助诊断视频回闪问题
    echo 测试时长: 10秒 x 3配置 = 30秒
    echo 测试过程中会显示视频窗口，按ESC可提前退出
    echo.
    pause
    python test_video_continuity.py
) else if "%choice%"=="4" (
    goto show_suggestions
) else (
    echo 无效选择，退出
    pause
    exit /b 1
)

echo.
echo 测试完成！请查看日志文件获取详细信息
pause
exit /b 0

:show_suggestions
echo.
echo ===============================
echo 性能优化建议
echo ===============================
echo.
echo 主要优化点:
echo 1. 降低推理频率 - 从每帧推理改为每2-3帧推理一次
echo 2. 减小推理分辨率 - 从1920x1080降低到640x360
echo 3. 优化队列大小 - 减小队列缓冲区，降低延迟
echo 4. 减少帧复制 - 避免不必要的frame.copy()操作
echo 5. 改进FFmpeg参数 - 使用更快的编码预设和低延迟设置
echo 6. 简化告警检测 - 减少复杂的几何计算
echo 7. 非阻塞队列操作 - 使用put_nowait和get_nowait
echo.
echo 预期改进效果:
echo - FPS提升: 20-40%%
echo - CPU使用率降低: 15-30%%
echo - 内存使用优化: 10-20%%
echo - 推流延迟减少: 30-50%%
echo.
echo 具体修改的关键代码行:
echo - Line 248: 推理频率控制和分辨率调整
echo - Queue大小优化: maxsize从10降低到2-5
echo - FFmpeg参数: preset改为ultrafast，启用zerolatency
echo.
pause
