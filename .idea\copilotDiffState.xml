<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/drDet_lc.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/drDet_lc.py" />
              <option name="originalContent" value="# -*- coding: utf-8 -*-&#10;&quot;&quot;&quot;&#10;Created on Thu Jun 19 19:30:02 2025&#10;&#10;@author: CRSC-CS&#10;&quot;&quot;&quot;&#10;&#10;#基于test10,将多线程改为多进程&#10;import cv2&#10;import os&#10;import time&#10;import queue&#10;import threading&#10;import multiprocessing&#10;from collections import deque&#10;import signal&#10;import atexit&#10;import psutil&#10;&#10;from ultralytics import YOLO&#10;import subprocess&#10;import logging&#10;import json&#10;&#10;from nan import minio_update&#10;from nan import drones_server&#10;from nan.drones_server import login_token_get&#10;from nan.drones_server import active_drones_get&#10;from nan.drones_server import drone_state_get&#10;from nan.drones_server import drone_yaw_get&#10;from nan.logger_config import setup_logging&#10;&#10;# 导入轨迹线接口&#10;from trajectory_interface import TrajectoryRenderer&#10;&#10;&#10;logging.basicConfig(&#10;    format=&quot;%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s&quot;,&#10;    level=logging.INFO,&#10;    filename=&quot;aip.log&quot;,&#10;    filemode=&quot;a&quot;,&#10;)&#10;&#10;class multiDealImg(object):&#10;    model = None  # 类变量，共享模型&#10;    model_lock = threading.Lock()  # 模型推理锁&#10;&#10;    def __init__(self, model_path, save_path, dict_json):&#10;        if multiDealImg.model is None:&#10;            multiDealImg.model = YOLO(model_path).to('cuda')&#10;        self.model = multiDealImg.model&#10;        self.is_switchOn = False&#10;        self.video_name = &quot;&quot;&#10;        self.mission_id = &quot;&quot;&#10;        self.result_path = os.path.dirname(save_path)&#10;        self.dict_json = dict_json  # 存储 initial_streams&#10;        self.save_full_video_path = save_path&#10;        &#10;        self.lock = threading.Lock()&#10;&#10;        # 添加资源管理相关属性&#10;        self.threads = []  # 存储所有线程&#10;        self.pipes = []    # 存储所有FFmpeg进程&#10;        self.cleanup_done = False  # 防止重复清理&#10;&#10;        # 注册清理函数&#10;        atexit.register(self.cleanup_resources)&#10;&#10;        self.save_queue = queue.Queue(maxsize=10)&#10;        self.push_queue = queue.LifoQueue(maxsize=3)&#10;        # 移除不必要的new_queue，直接使用frame_queue存储原始帧&#10;        self.frame_queue = queue.Queue(maxsize=1)&#10;&#10;        # self.names = [&quot;HelAndRef&quot;, &quot;Ref&quot;, &quot;excavator&quot;, &quot;NoHelAndRef&quot;, &quot;Hel&quot;]&#10;        self.names = [&quot;excavator&quot;, &quot;crane&quot;, &quot;compactor&quot;, &quot;tank truck&quot;, &quot;loader&quot;]&#10;        self.results = None&#10;        self.dataT = 2&#10;        &#10;&#10;        # 告警相关&#10;        self.buffer = deque(maxlen=125)  # 5秒缓存（25fps）&#10;        self.alert_flag = False&#10;        self.alert_frames = []&#10;        self.alert_counter = 0&#10;        self.alert_lock = threading.Lock()&#10;        self.alert_info = {}  # 存储告警信息&#10;        &#10;        self.drone_state = {}&#10;        &#10;        self.drone_angle = 0&#10;        # self.drone_angle = self.dict_json[&quot;attitude_head&quot;]&#10;        &#10;        # 轨迹线动态参数设置（可手动调整）&#10;        self.trajectory_length_multiplier = 2.0  # 轨迹线长度为视频高度的倍数&#10;        self.trajectory_width_ratio = 0.5  # 两条轨迹线间距为视频宽度的比例&#10;        &#10;        # 手动轨迹线参数（设置为None时使用动态计算）&#10;        self.manual_trajectory_length = None  # 手动设置的轨迹线长度（像素）&#10;        self.manual_trajectory_width = None   # 手动设置的轨迹线宽度（像素）&#10;        &#10;        # 初始化轨迹线渲染器，启用动态尺寸调整&#10;        self.trajectory_renderer = TrajectoryRenderer(&#10;            enable_dynamic_sizing=True,&#10;            length_multiplier=self.trajectory_length_multiplier,&#10;            width_ratio=self.trajectory_width_ratio&#10;        )&#10;        &#10;        # 设置手动参数（如果有的话）&#10;        if self.manual_trajectory_length is not None or self.manual_trajectory_width is not None:&#10;            self.trajectory_renderer.set_manual_params(&#10;                length=self.manual_trajectory_length,&#10;                width=self.manual_trajectory_width&#10;            )&#10;        &#10;        # # 模拟航向角变化相关参数&#10;        # self.simulated_heading = 0.0  # 当前模拟航向角&#10;        # self.heading_direction = 1  # 航向角变化方向 (1为正向，-1为反向)&#10;        # self.heading_change_rate = 5  # 航向角变化速率（度/帧）&#10;        # self.heading_min = -180  # 最小航向角&#10;        # self.heading_max = 180  # 最大航向角&#10;        # self.frame_count = 0      # 帧计数器&#10;        &#10;    # def update_simulated_heading(self):&#10;    #     &quot;&quot;&quot;&#10;    #     更新模拟航向角，实现在-90到90度之间的往复变化&#10;    #     &quot;&quot;&quot;&#10;    #     # 更新航向角&#10;    #     self.simulated_heading += self.heading_direction * self.heading_change_rate&#10;        &#10;        # 检查边界并改变方向&#10;        # if self.simulated_heading &gt;= self.heading_max:&#10;        #     self.simulated_heading = self.heading_max&#10;        #     self.heading_direction = -1  # 改为负方向&#10;        # elif self.simulated_heading &lt;= self.heading_min:&#10;        #     self.simulated_heading = self.heading_min&#10;        #     self.heading_direction = 1   # 改为正方向&#10;        &#10;        # return self.simulated_heading&#10;        &#10;        &#10;    def open_ffmpeg_process(self, output_stream):&#10;        self.command = [&#10;            'ffmpeg',&#10;            '-y',&#10;            '-re',&#10;            '-f', 'rawvideo',&#10;            '-vcodec', 'rawvideo',&#10;            '-pix_fmt', 'bgr24',&#10;            '-s', '{}x{}'.format(1920, 1280),&#10;            '-r', '30',&#10;            '-i', '-',&#10;            '-c:v', 'h264_nvenc',&#10;            '-preset', 'fast',&#10;            '-g', '60',&#10;            '-b:v', '1M',  # 目标码率&#10;            '-maxrate', '1.5M',  # 最大码率&#10;            '-bufsize', '2M',  # 缓冲区大小&#10;            '-r', '30',&#10;            '-pix_fmt', 'yuv420p',&#10;            '-f', 'flv',&#10;            output_stream&#10;        ]&#10;        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE)&#10;&#10;    def openFfmpegSaveVideo(self, outputSaveVideo):&#10;        ffmpeg_command = [&#10;            &quot;ffmpeg&quot;, &quot;-y&quot;, &quot;-f&quot;, &quot;rawvideo&quot;, &quot;-vcodec&quot;, &quot;rawvideo&quot;,&#10;            &quot;-pix_fmt&quot;, &quot;bgr24&quot;, &quot;-s&quot;, &quot;1920x1280&quot;, &quot;-r&quot;, &quot;30&quot;, &quot;-i&quot;, &quot;-&quot;,&#10;            &quot;-c:v&quot;, &quot;h264_nvenc&quot;, &quot;-pix_fmt&quot;, &quot;yuv420p&quot;, &quot;-preset&quot;, &quot;fast&quot;,&#10;            &quot;-b:v&quot;, &quot;2M&quot;, &quot;-maxrate&quot;, &quot;3M&quot;, &quot;-bufsize&quot;, &quot;4M&quot;, outputSaveVideo&#10;        ]&#10;        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE)&#10;&#10;    def read_video(self, video_path):&#10;        cap = cv2.VideoCapture(video_path)&#10;        logging.info(f&quot;Attempting to open video stream: {video_path}&quot;)&#10;&#10;        if not cap.isOpened():&#10;            logging.error(f&quot;Failed to open video: {video_path}&quot;)&#10;            return&#10;        else:&#10;            logging.info(f&quot;Successfully opened video stream: {video_path}&quot;)&#10;    &#10;        try:&#10;            count = 0&#10;            &#10;            while self.is_switchOn:&#10;                ret, frame = cap.read()&#10;                if not ret:  # 视频结束或读取失败&#10;                    logging.info(f&quot;Video {video_path} ended or read failed.&quot;)&#10;                    break&#10;    &#10;                # 检查帧是否为空&#10;                if frame is None or frame.size == 0:&#10;                    logging.warning(&quot;Empty frame received, skipping.&quot;)&#10;                    continue&#10;    &#10;                # 调整帧大小&#10;                try:&#10;                    frame = cv2.resize(frame, (1920, 1280))&#10;                except Exception as e:&#10;                    logging.error(f&quot;Failed to resize frame: {e}&quot;)&#10;                    continue&#10;    &#10;                # 将原始帧放入队列供process_frame处理&#10;                if not self.frame_queue.full():&#10;                    self.frame_queue.put(frame)&#10;                    count += 1&#10;                    if count % 100 == 0:&#10;                        logging.info(f&quot;Frame {count} added to frame_queue&quot;)&#10;&#10;        except Exception as e:&#10;            logging.error(f&quot;Video read error: {e}&quot;)&#10;        finally:&#10;            cap.release()&#10;            logging.info(f&quot;Video {video_path} released.&quot;)&#10;&#10;    def process_frame(self):&#10;        try:&#10;            count = 0&#10;            alarmLevel = &quot;2&quot;&#10;            alarmClass = &quot;excavator&quot;&#10;            self.drone_angle = drone_yaw_get(self.dict_json)&#10;            logging.info(f'process_frame中的航向角更新')&#10;            print(self.drone_angle)&#10;    &#10;            while self.is_switchOn:&#10;                t1 = time.time()&#10;                frame = self.frame_queue.get()&#10;                # frame = cv2.resize(frame, (640, 360))&#10;                with self.model_lock:&#10;                    self.results = self.model.track(frame, conf=0.45, show_conf=False, verbose=False, tracker='bytetrack.yaml')&#10;                    # print(self.results)&#10;                    if count % 50 == 0:&#10;                        detection_count = len(self.results[0].boxes) if self.results and len(self.results) &gt; 0 else 0&#10;                        logging.info(f&quot;Detection results obtained: {detection_count} objects detected&quot;)&#10;                t2 = time.time()&#10;    &#10;                # 检测告警&#10;                alert_triggered = False&#10;                for result in self.results:&#10;                    for box in result.boxes:&#10;                        cls = int(box.cls)&#10;                    &#10;                        # 获取框坐标&#10;                        x1, y1, x2, y2 = map(int, box.xyxy[0])&#10;                        &#10;                        center_x = (x1 + x2) // 2&#10;                        center_y = (y1 + y2) // 2&#10;                        &#10;                        # 使用trajectory_interface计算轨迹线边界&#10;                        frame_height, frame_width = frame.shape[:2]&#10;                        trajectory_center_x = frame_width // 2  # 轨迹线中心X坐标&#10;                        trajectory_start_y = frame_height  # 轨迹线起始Y坐标&#10;                        &#10;                        # 获取轨迹线边界&#10;                        boundaries = self.trajectory_renderer.get_trajectory_boundaries(&#10;                            center_x=trajectory_center_x,&#10;                            start_y=trajectory_start_y,&#10;                            heading_angle=self.drone_angle,&#10;                            frame_width=frame_width,&#10;                            frame_height=frame_height,&#10;                            target_y=center_y&#10;                        )&#10;                        &#10;                        # 判断物体位置&#10;                        red_left = boundaries['red_left']&#10;                        red_right = boundaries['red_right']&#10;                        white_left = boundaries['white_left']&#10;                        white_right = boundaries['white_right']&#10;                        &#10;                        if red_left &lt;= center_x &lt;= red_right:&#10;                            # 位于两条红色轨迹线之间&#10;                            alarmLevel = &quot;1&quot;&#10;                        elif (white_left &lt;= center_x &lt; red_left) or (red_right &lt; center_x &lt;= white_right):&#10;                            # 位于白色和红色轨迹线之间&#10;                            alarmLevel = &quot;2&quot;&#10;                        else:&#10;                            # 位于其他区域&#10;                            alarmLevel = &quot;0&quot;&#10;                            &#10;                        alarmClass = self.names[cls]&#10;                        if alarmLevel == &quot;1&quot; or alarmLevel == &quot;2&quot;:&#10;                            alert_triggered = True&#10;                            break&#10;                    if alert_triggered:&#10;                        break&#10;    &#10;                # 处理告警逻辑&#10;                with self.alert_lock:&#10;                    if alert_triggered:  # 即时触发告警保存&#10;                        if not self.alert_flag:&#10;                            # 保存前五秒的帧（当前buffer中的所有内容）&#10;                            self.alert_frames = list(self.buffer)&#10;                            self.alert_flag = True&#10;                            self.alert_counter = 0&#10;    &#10;                    # 缓存当前帧&#10;                    self.buffer.append(frame.copy())&#10;    &#10;                    if self.alert_flag:&#10;                        self.alert_frames.append(frame.copy())&#10;                        # self.alert_counter += 1&#10;                        # if self.alert_counter &gt;= 150:  # 保存前后各五秒的帧（总共约10秒）&#10;                        self.save_alert_video(alarmLevel, alarmClass)&#10;                        self.alert_flag = False&#10;                        self.alert_frames = []&#10;                        self.alert_counter = 0&#10;                print('333行绘制检测框')&#10;                # 绘制检测框&#10;                self.save_queue.put(frame.copy())   #放在这里是保存原视频&#10;                &#10;                # 创建带检测框和轨迹线的帧用于显示&#10;                display_frame = frame.copy()&#10;                &#10;                # 先绘制轨迹线&#10;                display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)&#10;                &#10;                # 再绘制检测框 &#10;                try:&#10;                    if self.results:&#10;                        # 使用第一个结果进行绘制，需要在display_frame上绘制&#10;                        display_frame = self.results[0].plot(img=display_frame, conf=True, line_width=2, font_size=0.5)&#10;                        logging.info(f'结果绘制成功')&#10;                except Exception as e:&#10;                    logging.error(f&quot;Error plotting detection results: {e}&quot;)&#10;                &#10;                # 将带检测框和轨迹线的帧放入推流队列&#10;                if not self.push_queue.full():&#10;                    self.push_queue.put(display_frame)&#10;                    logging.info(f'将带检测框和轨迹线的帧放入推流队列')&#10;                &#10;                # 保存带检测框和轨迹线的帧到文件夹&#10;                if count % 30 == 0:  # 每30帧保存一次，避免保存过多图片&#10;                    try:&#10;                        # 创建保存目录&#10;                        save_dir = os.path.join(self.result_path, &quot;detection_frames&quot;)&#10;                        if not os.path.exists(save_dir):&#10;                            os.makedirs(save_dir)&#10;                            logging.info(f&quot;Created detection frames directory: {save_dir}&quot;)&#10;                        &#10;                        # 生成文件名&#10;                        timestamp = time.strftime(&quot;%Y%m%d_%H%M%S&quot;)&#10;                        frame_filename = f&quot;detection_frame_{timestamp}_{count}.jpg&quot;&#10;                        frame_path = os.path.join(save_dir, frame_filename)&#10;                        &#10;                        # 保存图片&#10;                        cv2.imwrite(frame_path, display_frame)&#10;                        &#10;                        logging.info(f&quot;Detection frame saved: {frame_path}&quot;)&#10;                    except Exception as e:&#10;                        logging.error(f&quot;Failed to save detection frame: {e}&quot;)&#10;                &#10;                &#10;                self.dataT = time.time() - t1&#10;    &#10;                if count % 100 == 99:&#10;                    logging.info(f&quot;Inference time: {self.dataT:.4f}s&quot;)&#10;                    count = 0&#10;                count += 1&#10;        except Exception as e:&#10;            logging.error(f&quot;Frame processing error: {e}&quot;)&#10;&#10;        &#10;    def save_alert_video(self, alarm_level, alarmClass):&#10;        if not self.alert_frames:&#10;            return&#10;        &#10;        # 创建以 video_name 命名的文件夹&#10;        alert_video_dir = os.path.join(self.result_path, self.video_name)&#10;        &#10;        if not os.path.exists(alert_video_dir):&#10;            os.makedirs(alert_video_dir)&#10;            logging.info(f&quot;Created folder: {alert_video_dir}&quot;)&#10;        &#10;        # 生成时间戳&#10;        timestamp = time.strftime(&quot;%Y%m%d-%H%M%S&quot;)&#10;        &#10;        # 告警视频路径&#10;        alert_video_path = os.path.join(&#10;            alert_video_dir, &#10;            f&quot;alert_{self.video_name}_{timestamp}.mp4&quot;&#10;        )&#10;        &#10;        # 告警图片路径&#10;        alert_image_path = os.path.join(&#10;            alert_video_dir, &#10;            f&quot;alert_{self.video_name}_{timestamp}.jpg&quot;&#10;        )&#10;        &#10;        # 保存告警图片&#10;        try:&#10;            cv2.imwrite(alert_image_path, self.alert_frames[-1])  # 保存告警图片&#10;            logging.info(f&quot;Alert image saved successfully: {alert_image_path}&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;Failed to save alert image: {e}&quot;)&#10;        &#10;        # 保存告警视频&#10;        threading.Thread(&#10;            target=self.write_alert_video, &#10;            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)&#10;        ).start()&#10;        &#10;        # 存储告警信息&#10;        self.alert_info = {&#10;            &quot;alert_image_path&quot;: alert_image_path,&#10;            &quot;alert_video_path&quot;: alert_video_path,&#10;            &quot;full_video_path&quot;: os.path.join(self.result_path, f&quot;{self.video_name}.mp4&quot;),&#10;            &quot;stream_url&quot;: self.dict_json[&quot;droneStreamUrl&quot;]&#10;        }&#10;        logging.info(f&quot;Alert info: {json.dumps(self.alert_info)}&quot;)&#10;        &#10;        # 调用 minio_update 和 alarm_info_post&#10;        # self.call_minio_and_alarm(alert_image_path, alert_video_path)&#10;        &#10;    &#10;    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):&#10;        # 调用 minio_update&#10;        logging.info(f&quot;Uploading image to MinIO: {alert_image_path}&quot;)&#10;        return_image_name = minio_update.minio_interface(&#10;            self.dict_json, &quot;alarm&quot;, os.path.basename(alert_image_path), alert_image_path&#10;        )&#10;        logging.info(f&quot;Image uploaded to MinIO: {return_image_name}&quot;)&#10;        &#10;        logging.info(f&quot;Uploading video to MinIO: {alert_video_path}&quot;)&#10;        return_video_name = minio_update.minio_interface(&#10;            self.dict_json, &quot;clip&quot;, os.path.basename(alert_video_path), alert_video_path&#10;        )&#10;        logging.info(f&quot;Video uploaded to MinIO: {return_video_name}&quot;)&#10;        &#10;        # 调用 alarm_info_post&#10;        logging.info(f&quot;Posting alarm info to server&quot;)&#10;        &#10;        self.drone_state = drone_state_get(self.dict_json)&#10;&#10;        drones_server.alarm_info_post(&#10;            active_drone=self.dict_json,&#10;            drone_state=self.drone_state,&#10;            classes=alarmClass,  # 告警类别&#10;            alarmLevel=alarm_level,  # 告警级别&#10;            alarmImageUrl=return_image_name,  # 告警图片路径&#10;            videoUrl=return_video_name  # 告警视频路径&#10;        )&#10;        logging.info(f&quot;Alarm info posted to server&quot;)&#10;&#10;    # @staticmethod&#10;    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):&#10;        command = [&#10;            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',&#10;            '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',&#10;            '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-preset', 'fast',&#10;            '-b:v', '2M', '-maxrate', '3M', '-bufsize', '4M', alert_video_path&#10;        ]&#10;        try:&#10;            pipe = subprocess.Popen(command, stdin=subprocess.PIPE)&#10;            for frame in frames:&#10;                pipe.stdin.write(frame.tobytes())&#10;            pipe.stdin.close()&#10;            pipe.wait()&#10;            logging.info(f&quot;Alert video saved: {alert_video_path}&quot;)&#10;            &#10;            # 异步调用 minio_update.minio_interface&#10;            threading.Thread(&#10;                target=self.call_minio_and_alarm, &#10;                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)&#10;            ).start()&#10;        except Exception as e:&#10;            logging.error(f&quot;Failed to save alert video: {e}&quot;)   &#10;&#10;                &#10;                &#10;    def pushImg(self):&#10;        try:&#10;            frame_count = 0  # 帧计数器&#10;            frame_interval = 1 / 30  # 目标帧率 30fps，每帧间隔 0.033 秒&#10;            last_frame_time = time.time()  # 记录上一帧的时间&#10;            logging.info(f'推流线程启动')&#10;            while self.is_switchOn:&#10;                t_start = time.time()&#10;&#10;                # 优先从push_queue获取已处理的帧（带检测框和轨迹线）&#10;                if not self.push_queue.empty():&#10;                    frame = self.push_queue.get()&#10;                    logging.info(f'从push_queue获取帧进行推流')&#10;                else:&#10;                    time.sleep(0.001)  # 避免空队列时 CPU 占用过高&#10;                    continue&#10;                &#10;                # 推流&#10;                try:&#10;                    # 检查管道是否仍然有效&#10;                    if self.pipe.poll() is not None:&#10;                        logging.error(&quot;FFmpeg process has exited, stopping push&quot;)&#10;                        break&#10;                    &#10;                    self.pipe.stdin.write(frame.tobytes())&#10;                    if frame_count % 100 == 0:&#10;                        logging.info(f&quot;Frame {frame_count} successfully pushed to stream&quot;)&#10;                except BrokenPipeError:&#10;                    logging.error(&quot;Broken pipe detected, FFmpeg process may have crashed&quot;)&#10;                    break&#10;                except Exception as e:&#10;                    logging.error(f&quot;Failed to write frame to pipe: {e}&quot;)&#10;                    break&#10;                &#10;                # 计算处理时间&#10;                elapsed = time.time() - t_start&#10;                frame_count += 1&#10;                &#10;                # 每100帧打印一次平均处理时间&#10;                if frame_count % 100 == 0:&#10;                    logging.info(f&quot;push time =: {elapsed}&quot;)&#10;                &#10;                # 精确帧率控制&#10;                next_frame_time = last_frame_time + frame_interval&#10;                sleep_time = max(0.0, next_frame_time - time.time())&#10;                time.sleep(sleep_time)&#10;                &#10;                final_push_time = time.time() -  t_start                 &#10;                if frame_count % 100 == 0:&#10;                    logging.info(f&quot;final time =: {final_push_time}&quot;)&#10;                &#10;        except Exception as e:&#10;            logging.error(f&quot;Streaming error: {e}&quot;)&#10;        finally:&#10;            try:&#10;                self.pipe.stdin.close()&#10;                self.pipe.wait()&#10;            except Exception as e:&#10;                logging.error(f&quot;Failed to close pipe: {e}&quot;)&#10;&#10;                &#10;&#10;    def saveImg(self):&#10;        try:&#10;            while self.is_switchOn or not self.save_queue.empty():&#10;                if not self.save_queue.empty():&#10;                    frame = self.save_queue.get()&#10;                    try:&#10;                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())&#10;                    except Exception as e:&#10;                        logging.error(f&quot;Failed to write frame to save video: {e}&quot;)&#10;                        break&#10;                else:&#10;                    time.sleep(0.01)  # 避免空队列时 CPU 占用过高&#10;    &#10;            # 视频保存完成后，上传到 MinIO&#10;            if os.path.exists(self.save_full_video_path):&#10;                return_video_name = minio_update.minio_interface(&#10;                    self.dict_json, &#10;                    &quot;full&quot;, &#10;                    os.path.basename(self.save_full_video_path), &#10;                    self.save_full_video_path&#10;                )&#10;                logging.info(f&quot;Full video uploaded to MinIO: {return_video_name}&quot;)&#10;            else:&#10;                logging.error(f&quot;Full video file not found: {self.save_full_video_path}&quot;)&#10;    &#10;        except Exception as e:&#10;            logging.error(f&quot;Video save error: {e}&quot;)&#10;        finally:&#10;            self.ffmpegSaveVideo.stdin.close()&#10;            self.ffmpegSaveVideo.wait()&#10;            logging.info(&quot;Video save process completed.&quot;)&#10;&#10;        &#10;    def set_switch_on(self, listenVule):&#10;        self.is_switchOn = listenVule&#10;        &#10;    def set_video_name(self, video_name):&#10;        self.video_name = video_name&#10;&#10;    def set_mission_id(self, mission_id):&#10;        self.mission_id = mission_id&#10;&#10;    def _listen_for_stop(self):&#10;        while self.is_switchOn:&#10;            user_input = input(&quot;输入 'stop' 关闭视频流: &quot;)&#10;            if user_input.strip().lower() == 'stop':&#10;                self.is_switchOn = False&#10;                logging.info(&quot;收到关闭信号，正在关闭视频流...&quot;)&#10;                break&#10;            &#10;    def listen_for_alert(self):&#10;        while self.is_switchOn:&#10;            if self.alert_flag:&#10;                # 当 alert_flag 为 True 时执行特定操作&#10;                self.handle_alert()&#10;                self.alert_flag = False  # 重置 alert_flag&#10;            time.sleep(0.1)  # 每 0.1 秒检查一次&#10;    def handle_alert(self):&#10;        # 在这里实现当 alert_flag 为 True 时需要执行的操作&#10;        logging.info(&quot;Alert detected! Handling alert...&quot;)&#10;        self.drone_state = drone_state_get(self.dict_json)&#10;                &#10;    &#10;    # 原有的轨迹线绘制相关方法已被trajectory_interface.py替代&#10;&#10;    def startThread1(self, input_stream, output_stream, saveVideoPath):&#10;        try:&#10;            self.open_ffmpeg_process(output_stream)&#10;            self.openFfmpegSaveVideo(saveVideoPath)&#10;            threads = [&#10;                threading.Thread(target=self.read_video, args=(input_stream,)),&#10;                threading.Thread(target=self.process_frame),&#10;                threading.Thread(target=self.pushImg),&#10;                threading.Thread(target=self.saveImg)&#10;            ]&#10;            for t in threads:&#10;                t.daemon = True&#10;                t.start()&#10;            for t in threads:&#10;                t.join()&#10;        except Exception as e:&#10;            logging.error(f&quot;Thread start failed: {e}&quot;)&#10;            &#10;    def check_pipe_health(self):&#10;        while self.is_switchOn:&#10;            if self.pipe.poll() is not None:  # 检查 FFmpeg 进程是否已退出&#10;                logging.error(&quot;FFmpeg process has exited unexpectedly.&quot;)&#10;                self.is_switchOn = False&#10;                break&#10;            time.sleep(5)  # 每5秒检查一次&#10;            &#10;    def startThread(self, input_stream, output_stream, saveVideoPath):&#10;        try:&#10;            self.open_ffmpeg_process(output_stream)&#10;            self.openFfmpegSaveVideo(saveVideoPath)&#10;&#10;            # 创建线程并添加到管理列表&#10;            threads = [&#10;                threading.Thread(target=self.read_video, args=(input_stream,), name=&quot;read_video&quot;),&#10;                threading.Thread(target=self.process_frame, name=&quot;process_frame&quot;),&#10;                threading.Thread(target=self.pushImg, name=&quot;pushImg&quot;),&#10;                threading.Thread(target=self.saveImg, name=&quot;saveImg&quot;),&#10;                threading.Thread(target=self.check_pipe_health, name=&quot;check_pipe_health&quot;)&#10;            ]&#10;&#10;            # 设置线程为daemon并启动&#10;            for t in threads:&#10;                t.daemon = True&#10;                self.threads.append(t)&#10;                t.start()&#10;&#10;            # 等待所有线程完成&#10;            for t in threads:&#10;                t.join()&#10;&#10;        except KeyboardInterrupt:&#10;            logging.info(&quot;收到键盘中断信号，正在停止线程...&quot;)&#10;            self.is_switchOn = False&#10;        except Exception as e:&#10;            logging.error(f&quot;线程启动失败: {e}&quot;)&#10;        finally:&#10;            # 确保资源被清理&#10;            self.cleanup_resources()&#10;&#10;    def cleanup_resources(self):&#10;        &quot;&quot;&quot;清理所有资源&quot;&quot;&quot;&#10;        if self.cleanup_done:&#10;            return&#10;&#10;        logging.info(&quot;开始清理资源...&quot;)&#10;&#10;        # 1. 设置停止标志&#10;        self.is_switchOn = False&#10;&#10;        # 2. 清理FFmpeg进程&#10;        try:&#10;            if hasattr(self, 'pipe') and self.pipe:&#10;                if self.pipe.poll() is None:  # 进程仍在运行&#10;                    self.pipe.stdin.close()&#10;                    self.pipe.terminate()&#10;                    self.pipe.wait(timeout=5)&#10;                logging.info(&quot;FFmpeg推流进程已关闭&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;关闭FFmpeg推流进程时出错: {e}&quot;)&#10;            try:&#10;                if hasattr(self, 'pipe') and self.pipe:&#10;                    self.pipe.kill()&#10;            except:&#10;                pass&#10;&#10;        try:&#10;            if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:&#10;                if self.ffmpegSaveVideo.poll() is None:&#10;                    self.ffmpegSaveVideo.stdin.close()&#10;                    self.ffmpegSaveVideo.terminate()&#10;                    self.ffmpegSaveVideo.wait(timeout=5)&#10;                logging.info(&quot;FFmpeg保存进程已关闭&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;关闭FFmpeg保存进程时出错: {e}&quot;)&#10;            try:&#10;                if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:&#10;                    self.ffmpegSaveVideo.kill()&#10;            except:&#10;                pass&#10;&#10;        # 3. 等待所有线程结束&#10;        for thread in self.threads:&#10;            if thread.is_alive():&#10;                try:&#10;                    thread.join(timeout=2.0)&#10;                    if thread.is_alive():&#10;                        logging.warning(f&quot;线程 {thread.name} 超时未结束&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;等待线程结束时出错: {e}&quot;)&#10;&#10;        # 4. 清空所有队列&#10;        try:&#10;            while not self.frame_queue.empty():&#10;                self.frame_queue.get_nowait()&#10;            while not self.push_queue.empty():&#10;                self.push_queue.get_nowait()&#10;            while not self.save_queue.empty():&#10;                self.save_queue.get_nowait()&#10;            logging.info(&quot;所有队列已清空&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;清空队列时出错: {e}&quot;)&#10;&#10;        self.cleanup_done = True&#10;        logging.info(&quot;资源清理完成&quot;)&#10;&#10;class StreamManager:&#10;    def __init__(self, model_path):&#10;        self.model_path = model_path&#10;        self.streams = []&#10;        self.lock = multiprocessing.Lock()&#10;        self.processor = VideoStreamProcessor(model_path)&#10;        self.active_processes = {}&#10;        self.cleanup_done = False&#10;&#10;        # 注册信号处理器和清理函数&#10;        signal.signal(signal.SIGINT, self.signal_handler)&#10;        signal.signal(signal.SIGTERM, self.signal_handler)&#10;        atexit.register(self.cleanup_all_resources)&#10;&#10;    def signal_handler(self, signum, frame):&#10;        &quot;&quot;&quot;处理系统信号&quot;&quot;&quot;&#10;        logging.info(f&quot;收到信号 {signum}，开始清理所有资源...&quot;)&#10;        self.cleanup_all_resources()&#10;&#10;    def cleanup_all_resources(self):&#10;        &quot;&quot;&quot;清理所有流管理器的资源&quot;&quot;&quot;&#10;        if self.cleanup_done:&#10;            return&#10;&#10;        logging.info(&quot;开始清理StreamManager资源...&quot;)&#10;&#10;        with self.lock:&#10;            # 停止所有活动进程&#10;            for input_path, process in list(self.active_processes.items()):&#10;                try:&#10;                    if process.is_alive():&#10;                        logging.info(f&quot;正在停止进程: {input_path}&quot;)&#10;                        self.processor.stop_stream(input_path)&#10;                        process.terminate()&#10;                        process.join(timeout=5)&#10;                        if process.is_alive():&#10;                            logging.warning(f&quot;进程 {input_path} 超时未结束，强制杀死&quot;)&#10;                            process.kill()&#10;                            process.join()&#10;                        logging.info(f&quot;进程 {input_path} 已停止&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;停止进程 {input_path} 时出错: {e}&quot;)&#10;&#10;            self.active_processes.clear()&#10;            self.streams.clear()&#10;&#10;        self.cleanup_done = True&#10;        logging.info(&quot;StreamManager资源清理完成&quot;)&#10;&#10;    def add_stream(self, dict_json, out_res):&#10;        input_path = dict_json[&quot;droneStreamUrl&quot;]&#10;        output_url = dict_json[&quot;aiStreamUrl&quot;]&#10;        video_name = dict_json[&quot;taskId&quot;]&#10;        &#10;        with self.lock:&#10;            # 创建保存文件夹&#10;            save_folder = os.path.join(out_res, video_name)&#10;            if not os.path.exists(save_folder):&#10;                os.makedirs(save_folder)&#10;                logging.info(f&quot;Created folder: {save_folder}&quot;)&#10;&#10;            save_path = os.path.join(save_folder, f&quot;{video_name}.mp4&quot;)&#10;&#10;            # 启动新流的处理进程&#10;            process = multiprocessing.Process(&#10;                target=self.processor.process_stream,&#10;                args=(dict_json, save_path),&#10;                name=f&quot;stream_{video_name}&quot;&#10;            )&#10;            process.start()&#10;            logging.info(f'推流进程已开启: {video_name}')&#10;&#10;            # 存储进程信息&#10;            self.active_processes[input_path] = process&#10;            self.streams.append({&#10;                &quot;input&quot;: input_path,&#10;                &quot;output&quot;: output_url,&#10;                &quot;save_path&quot;: save_path,&#10;                &quot;video_name&quot;: video_name&#10;            })&#10;            logging.info(f&quot;Stream {output_url} has been added and started.&quot;)&#10;&#10;    def stop_stream(self, input_url):&#10;        with self.lock:&#10;            if input_url in self.active_processes:&#10;                process = self.active_processes[input_url]&#10;                try:&#10;                    # 设置流的开关为 False&#10;                    self.processor.stop_stream(input_url)&#10;&#10;                    # 等待进程正常结束&#10;                    process.join(timeout=10)&#10;&#10;                    # 如果进程仍在运行，强制终止&#10;                    if process.is_alive():&#10;                        logging.warning(f&quot;进程 {input_url} 超时未结束，强制终止&quot;)&#10;                        process.terminate()&#10;                        process.join(timeout=5)&#10;&#10;                        if process.is_alive():&#10;                            process.kill()&#10;                            process.join()&#10;&#10;                    del self.active_processes[input_url]&#10;&#10;                    # 从流列表中移除&#10;                    self.streams = [s for s in self.streams if s[&quot;input&quot;] != input_url]&#10;&#10;                    logging.info(f&quot;Stream {input_url} has been stopped.&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;停止流 {input_url} 时出错: {e}&quot;)&#10;            else:&#10;                logging.warning(f&quot;Stream {input_url} not found.&quot;)&#10;&#10;    def get_streams(self):&#10;        with self.lock:&#10;            return self.streams&#10;&#10;    def is_stream_active(self, input_url):&#10;        with self.lock:&#10;            return input_url in self.active_processes and self.active_processes[input_url].is_alive()&#10;&#10;class VideoStreamProcessor:&#10;&#10;    def __init__(self, model_path):&#10;        self.model_path = model_path&#10;        self.active_detectors = {}  # 用于存储活动的检测器&#10;        self.lock = multiprocessing.Lock()  # 用于进程安全的操作&#10;&#10;    def process_stream(self, dict_json, save_path):&#10;        input_path = dict_json[&quot;droneStreamUrl&quot;]&#10;        output_url = dict_json[&quot;aiStreamUrl&quot;]&#10;        video_name = dict_json[&quot;taskId&quot;]&#10;&#10;        detector = None&#10;        try:&#10;            detector = multiDealImg(self.model_path, save_path, dict_json)&#10;            detector.set_switch_on(True)&#10;            detector.set_video_name(video_name)&#10;&#10;            with self.lock:&#10;                self.active_detectors[input_path] = detector&#10;&#10;            detector.startThread(input_path, output_url, save_path)&#10;&#10;        except Exception as e:&#10;            logging.error(f&quot;处理流 {input_path} 时出错: {e}&quot;)&#10;        finally:&#10;            # 确保资源被清理&#10;            if detector:&#10;                detector.cleanup_resources()&#10;&#10;            with self.lock:&#10;                if input_path in self.active_detectors:&#10;                    del self.active_detectors[input_path]&#10;            logging.info(f&quot;Stream {input_path} 处理完成并清理资源&quot;)&#10;&#10;    def stop_stream(self, input_path):&#10;        with self.lock:&#10;            if input_path in self.active_detectors:&#10;                detector = self.active_detectors[input_path]&#10;                detector.set_switch_on(False)&#10;                detector.cleanup_resources()&#10;                logging.info(f&quot;Detector for stream {input_path} has been stopped.&quot;)&#10;            else:&#10;                logging.warning(f&quot;Detector for stream {input_path} not found.&quot;)&#10;&#10;    def is_detector_active(self, input_path):&#10;        with self.lock:&#10;            return input_path in self.active_detectors&#10;&#10;# 定义比较函数，基于指定的关键字&#10;def is_equal(dict1, dict2, keys):&#10;    return all(dict1.get(key) == dict2.get(key) for key in keys)&#10;&#10;# 添加全局清理函数&#10;def setup_global_cleanup():&#10;    &quot;&quot;&quot;设置全局资源清理&quot;&quot;&quot;&#10;    def cleanup_handler(signum, frame):&#10;        logging.info(&quot;收到退出信号，正在清理全局资源...&quot;)&#10;        # 强制清理所有资源并退出&#10;        try:&#10;            # 获取当前进程的所有子进程&#10;            current_process = psutil.Process()&#10;            for child in current_process.children(recursive=True):&#10;                try:&#10;                    child.terminate()&#10;                    child.wait(timeout=3)&#10;                    if child.is_running():&#10;                        child.kill()&#10;                except (psutil.NoSuchProcess, psutil.TimeoutExpired):&#10;                    pass&#10;        except Exception as e:&#10;            logging.error(f&quot;清理子进程时出错: {e}&quot;)&#10;&#10;        logging.info(&quot;全局资源清理完成&quot;)&#10;        os._exit(0)&#10;&#10;    signal.signal(signal.SIGINT, cleanup_handler)&#10;    signal.signal(signal.SIGTERM, cleanup_handler)&#10;&#10;if __name__ == &quot;__main__&quot;:&#10;    # 设置全局清理&#10;    setup_global_cleanup()&#10;&#10;    setup_logging()&#10;&#10;    # model_path = &quot;./model/best0701_640_2.pt&quot;&#10;    model_path = &quot;/root/ultralytics-main/model/best_cj.pt&quot;&#10;    out_res = &quot;./res&quot;&#10;    stream_manager = StreamManager(model_path)&#10;&#10;    # 定义关键字列表&#10;    keys = [&quot;droneStreamUrl&quot;]&#10;&#10;    presentStream = []&#10;&#10;    try:&#10;        while True:&#10;            # 获取token&#10;            login_token_get()&#10;            # 获取正在飞行任务中的无人机列表&#10;            active_drones_list = active_drones_get()&#10;            # 获取 active_drones_list 中有且 presentStream 中没有的元素&#10;            drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]&#10;            print(drones_unique)&#10;&#10;            if(len(drones_unique) &gt; 0):&#10;                for active_drone in drones_unique:&#10;                    print(&quot;现在执行active_drone的for循环&quot;)&#10;                    stream_manager.add_stream(active_drone, out_res)&#10;            else:&#10;                print(presentStream)&#10;                # print('111111111111111未检测')&#10;&#10;            presentStream = active_drones_list&#10;            time.sleep(15)&#10;&#10;    except KeyboardInterrupt:&#10;        logging.info(&quot;收到键盘中断，正在关闭程序...&quot;)&#10;    except Exception as e:&#10;        logging.error(f&quot;主程序运行出错: {e}&quot;)&#10;    finally:&#10;        # 确保清理所有资源&#10;        stream_manager.cleanup_all_resources()&#10;        logging.info(&quot;程序正常退出&quot;)&#10;" />
              <option name="updatedContent" value="# -*- coding: utf-8 -*-&#10;&quot;&quot;&quot;&#10;Created on Thu Jun 19 19:30:02 2025&#10;&#10;@author: CRSC-CS&#10;&quot;&quot;&quot;&#10;&#10;#基于test10,将多线程改为多进程&#10;import cv2&#10;import os&#10;import time&#10;import queue&#10;import threading&#10;import multiprocessing&#10;from collections import deque&#10;import signal&#10;import atexit&#10;import psutil&#10;&#10;from ultralytics import YOLO&#10;import subprocess&#10;import logging&#10;import json&#10;&#10;from nan import minio_update&#10;from nan import drones_server&#10;from nan.drones_server import login_token_get&#10;from nan.drones_server import active_drones_get&#10;from nan.drones_server import drone_state_get&#10;from nan.drones_server import drone_yaw_get&#10;from nan.logger_config import setup_logging&#10;&#10;# 导入轨迹线接口&#10;from trajectory_interface import TrajectoryRenderer&#10;&#10;&#10;logging.basicConfig(&#10;    format=&quot;%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s&quot;,&#10;    level=logging.INFO,&#10;    filename=&quot;aip.log&quot;,&#10;    filemode=&quot;a&quot;,&#10;)&#10;&#10;class multiDealImg(object):&#10;    model = None  # 类变量，共享模型&#10;    model_lock = threading.Lock()  # 模型推理锁&#10;&#10;    def __init__(self, model_path, save_path, dict_json):&#10;        if multiDealImg.model is None:&#10;            multiDealImg.model = YOLO(model_path).to('cuda')&#10;        self.model = multiDealImg.model&#10;        self.is_switchOn = False&#10;        self.video_name = &quot;&quot;&#10;        self.mission_id = &quot;&quot;&#10;        self.result_path = os.path.dirname(save_path)&#10;        self.dict_json = dict_json  # 存储 initial_streams&#10;        self.save_full_video_path = save_path&#10;        &#10;        self.lock = threading.Lock()&#10;&#10;        # 添加资源管理相关属性&#10;        self.threads = []  # 存储所有线程&#10;        self.pipes = []    # 存储所有FFmpeg进程&#10;        self.cleanup_done = False  # 防止重复清理&#10;&#10;        # 注册清理函数&#10;        atexit.register(self.cleanup_resources)&#10;&#10;        self.save_queue = queue.Queue(maxsize=10)&#10;        self.push_queue = queue.LifoQueue(maxsize=3)&#10;        # 移除不必要的new_queue，直接使用frame_queue存储原始帧&#10;        self.frame_queue = queue.Queue(maxsize=1)&#10;&#10;        # self.names = [&quot;HelAndRef&quot;, &quot;Ref&quot;, &quot;excavator&quot;, &quot;NoHelAndRef&quot;, &quot;Hel&quot;]&#10;        self.names = [&quot;excavator&quot;, &quot;crane&quot;, &quot;compactor&quot;, &quot;tank truck&quot;, &quot;loader&quot;]&#10;        self.results = None&#10;        self.dataT = 2&#10;        &#10;&#10;        # 告警相关&#10;        self.buffer = deque(maxlen=125)  # 5秒缓存（25fps）&#10;        self.alert_flag = False&#10;        self.alert_frames = []&#10;        self.alert_counter = 0&#10;        self.alert_lock = threading.Lock()&#10;        self.alert_info = {}  # 存储告警信息&#10;        &#10;        self.drone_state = {}&#10;        &#10;        self.drone_angle = 0&#10;        # self.drone_angle = self.dict_json[&quot;attitude_head&quot;]&#10;        &#10;        # 轨迹线动态参数设置（可手动调整）&#10;        self.trajectory_length_multiplier = 2.0  # 轨迹线长度为视频高度的倍数&#10;        self.trajectory_width_ratio = 0.5  # 两条轨迹线间距为视频宽度的比例&#10;        &#10;        # 手动轨迹线参数（设置为None时使用动态计算）&#10;        self.manual_trajectory_length = None  # 手动设置的轨迹线长度（像素）&#10;        self.manual_trajectory_width = None   # 手动设置的轨迹线宽度（像素）&#10;        &#10;        # 初始化轨迹线渲染器，启用动态尺寸调整&#10;        self.trajectory_renderer = TrajectoryRenderer(&#10;            enable_dynamic_sizing=True,&#10;            length_multiplier=self.trajectory_length_multiplier,&#10;            width_ratio=self.trajectory_width_ratio&#10;        )&#10;        &#10;        # 设置手动参数（如果有的话）&#10;        if self.manual_trajectory_length is not None or self.manual_trajectory_width is not None:&#10;            self.trajectory_renderer.set_manual_params(&#10;                length=self.manual_trajectory_length,&#10;                width=self.manual_trajectory_width&#10;            )&#10;        &#10;        # # 模拟航向角变化相关参数&#10;        # self.simulated_heading = 0.0  # 当前模拟航向角&#10;        # self.heading_direction = 1  # 航向角变化方向 (1为正向，-1为反向)&#10;        # self.heading_change_rate = 5  # 航向角变化速率（度/帧）&#10;        # self.heading_min = -180  # 最小航向角&#10;        # self.heading_max = 180  # 最大航向角&#10;        # self.frame_count = 0      # 帧计数器&#10;        &#10;    # def update_simulated_heading(self):&#10;    #     &quot;&quot;&quot;&#10;    #     更新模拟航向角，实现在-90到90度之间的往复变化&#10;    #     &quot;&quot;&quot;&#10;    #     # 更新航向角&#10;    #     self.simulated_heading += self.heading_direction * self.heading_change_rate&#10;        &#10;        # 检查边界并改变方向&#10;        # if self.simulated_heading &gt;= self.heading_max:&#10;        #     self.simulated_heading = self.heading_max&#10;        #     self.heading_direction = -1  # 改为负方向&#10;        # elif self.simulated_heading &lt;= self.heading_min:&#10;        #     self.simulated_heading = self.heading_min&#10;        #     self.heading_direction = 1   # 改为正方向&#10;        &#10;        # return self.simulated_heading&#10;        &#10;        &#10;    def open_ffmpeg_process(self, output_stream):&#10;        # 优先尝试硬件编码，失败时自动回退到软件编码&#10;        self.command = [&#10;            'ffmpeg',&#10;            '-y',&#10;            '-re',&#10;            '-f', 'rawvideo',&#10;            '-vcodec', 'rawvideo',&#10;            '-pix_fmt', 'bgr24',&#10;            '-s', '{}x{}'.format(1920, 1280),&#10;            '-r', '30',&#10;            '-i', '-',&#10;            '-c:v', 'libx264',  # 使用软件编码器libx264&#10;            '-preset', 'fast',&#10;            '-crf', '23',  # libx264支持crf参数&#10;            '-g', '60',&#10;            '-r', '30',&#10;            '-pix_fmt', 'yuv420p',&#10;            '-f', 'flv',&#10;            output_stream&#10;        ]&#10;        try:&#10;            self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)&#10;            logging.info(&quot;FFmpeg推流进程启动成功 (使用libx264)&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;FFmpeg推流进程启动失败: {e}&quot;)&#10;            raise&#10;&#10;    def openFfmpegSaveVideo(self, outputSaveVideo):&#10;        ffmpeg_command = [&#10;            &quot;ffmpeg&quot;, &quot;-y&quot;, &quot;-f&quot;, &quot;rawvideo&quot;, &quot;-vcodec&quot;, &quot;rawvideo&quot;,&#10;            &quot;-pix_fmt&quot;, &quot;bgr24&quot;, &quot;-s&quot;, &quot;1920x1280&quot;, &quot;-r&quot;, &quot;30&quot;, &quot;-i&quot;, &quot;-&quot;,&#10;            &quot;-c:v&quot;, &quot;libx264&quot;, &quot;-pix_fmt&quot;, &quot;yuv420p&quot;, &quot;-preset&quot;, &quot;fast&quot;, &#10;            &quot;-crf&quot;, &quot;23&quot;, outputSaveVideo  # 使用软件编码器libx264&#10;        ]&#10;        try:&#10;            self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)&#10;            logging.info(&quot;FFmpeg保存进程启动成功 (使用libx264)&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;FFmpeg保存进程启动失败: {e}&quot;)&#10;            raise&#10;&#10;    def read_video(self, video_path):&#10;        cap = cv2.VideoCapture(video_path)&#10;        logging.info(f&quot;Attempting to open video stream: {video_path}&quot;)&#10;&#10;        if not cap.isOpened():&#10;            logging.error(f&quot;Failed to open video: {video_path}&quot;)&#10;            return&#10;        else:&#10;            logging.info(f&quot;Successfully opened video stream: {video_path}&quot;)&#10;    &#10;        try:&#10;            count = 0&#10;            &#10;            while self.is_switchOn:&#10;                ret, frame = cap.read()&#10;                if not ret:  # 视频结束或读取失败&#10;                    logging.info(f&quot;Video {video_path} ended or read failed.&quot;)&#10;                    break&#10;    &#10;                # 检查帧是否为空&#10;                if frame is None or frame.size == 0:&#10;                    logging.warning(&quot;Empty frame received, skipping.&quot;)&#10;                    continue&#10;    &#10;                # 调整帧大小&#10;                try:&#10;                    frame = cv2.resize(frame, (1920, 1280))&#10;                except Exception as e:&#10;                    logging.error(f&quot;Failed to resize frame: {e}&quot;)&#10;                    continue&#10;    &#10;                # 将原始帧放入队列供process_frame处理&#10;                if not self.frame_queue.full():&#10;                    self.frame_queue.put(frame)&#10;                    count += 1&#10;                    if count % 100 == 0:&#10;                        logging.info(f&quot;Frame {count} added to frame_queue&quot;)&#10;&#10;        except Exception as e:&#10;            logging.error(f&quot;Video read error: {e}&quot;)&#10;        finally:&#10;            cap.release()&#10;            logging.info(f&quot;Video {video_path} released.&quot;)&#10;&#10;    def process_frame(self):&#10;        try:&#10;            count = 0&#10;            alarmLevel = &quot;2&quot;&#10;            alarmClass = &quot;excavator&quot;&#10;            self.drone_angle = drone_yaw_get(self.dict_json)&#10;            logging.info(f'process_frame中的航向角更新')&#10;            print(self.drone_angle)&#10;    &#10;            while self.is_switchOn:&#10;                t1 = time.time()&#10;                frame = self.frame_queue.get()&#10;                # frame = cv2.resize(frame, (640, 360))&#10;                with self.model_lock:&#10;                    self.results = self.model.track(frame, conf=0.45, show_conf=False, verbose=False, tracker='bytetrack.yaml')&#10;                    # print(self.results)&#10;                    if count % 50 == 0:&#10;                        detection_count = len(self.results[0].boxes) if self.results and len(self.results) &gt; 0 else 0&#10;                        logging.info(f&quot;Detection results obtained: {detection_count} objects detected&quot;)&#10;                t2 = time.time()&#10;    &#10;                # 检测告警&#10;                alert_triggered = False&#10;                for result in self.results:&#10;                    for box in result.boxes:&#10;                        cls = int(box.cls)&#10;                    &#10;                        # 获取框坐标&#10;                        x1, y1, x2, y2 = map(int, box.xyxy[0])&#10;                        &#10;                        center_x = (x1 + x2) // 2&#10;                        center_y = (y1 + y2) // 2&#10;                        &#10;                        # 使用trajectory_interface计算轨迹线边界&#10;                        frame_height, frame_width = frame.shape[:2]&#10;                        trajectory_center_x = frame_width // 2  # 轨迹线中心X坐标&#10;                        trajectory_start_y = frame_height  # 轨迹线起始Y坐标&#10;                        &#10;                        # 获取轨迹线边界&#10;                        boundaries = self.trajectory_renderer.get_trajectory_boundaries(&#10;                            center_x=trajectory_center_x,&#10;                            start_y=trajectory_start_y,&#10;                            heading_angle=self.drone_angle,&#10;                            frame_width=frame_width,&#10;                            frame_height=frame_height,&#10;                            target_y=center_y&#10;                        )&#10;                        &#10;                        # 判断物体位置&#10;                        red_left = boundaries['red_left']&#10;                        red_right = boundaries['red_right']&#10;                        white_left = boundaries['white_left']&#10;                        white_right = boundaries['white_right']&#10;                        &#10;                        if red_left &lt;= center_x &lt;= red_right:&#10;                            # 位于两条红色轨迹线之间&#10;                            alarmLevel = &quot;1&quot;&#10;                        elif (white_left &lt;= center_x &lt; red_left) or (red_right &lt; center_x &lt;= white_right):&#10;                            # 位于白色和红色轨迹线之间&#10;                            alarmLevel = &quot;2&quot;&#10;                        else:&#10;                            # 位于其他区域&#10;                            alarmLevel = &quot;0&quot;&#10;                            &#10;                        alarmClass = self.names[cls]&#10;                        if alarmLevel == &quot;1&quot; or alarmLevel == &quot;2&quot;:&#10;                            alert_triggered = True&#10;                            break&#10;                    if alert_triggered:&#10;                        break&#10;    &#10;                # 处理告警逻辑&#10;                with self.alert_lock:&#10;                    if alert_triggered:  # 即时触发告警保存&#10;                        if not self.alert_flag:&#10;                            # 保存前五秒的帧（当前buffer中的所有内容）&#10;                            self.alert_frames = list(self.buffer)&#10;                            self.alert_flag = True&#10;                            self.alert_counter = 0&#10;    &#10;                    # 缓存当前帧&#10;                    self.buffer.append(frame.copy())&#10;    &#10;                    if self.alert_flag:&#10;                        self.alert_frames.append(frame.copy())&#10;                        # self.alert_counter += 1&#10;                        # if self.alert_counter &gt;= 150:  # 保存前后各五秒的帧（总共约10秒）&#10;                        self.save_alert_video(alarmLevel, alarmClass)&#10;                        self.alert_flag = False&#10;                        self.alert_frames = []&#10;                        self.alert_counter = 0&#10;                print('333行绘制检测框')&#10;                # 绘制检测框&#10;                self.save_queue.put(frame.copy())   #放在这里是保存原视频&#10;                &#10;                # 创建带检测框和轨迹线的帧用于显示&#10;                display_frame = frame.copy()&#10;                &#10;                # 先绘制轨迹线&#10;                display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)&#10;                &#10;                # 再绘制检测框 &#10;                try:&#10;                    if self.results:&#10;                        # 使用第一个结果进行绘制，需要在display_frame上绘制&#10;                        display_frame = self.results[0].plot(img=display_frame, conf=True, line_width=2, font_size=0.5)&#10;                        logging.info(f'结果绘制成功')&#10;                except Exception as e:&#10;                    logging.error(f&quot;Error plotting detection results: {e}&quot;)&#10;                &#10;                # 将带检测框和轨迹线的帧放入推流队列&#10;                if not self.push_queue.full():&#10;                    self.push_queue.put(display_frame)&#10;                    logging.info(f'将带检测框和轨迹线的帧放入推流队列')&#10;                &#10;                # 保存带检测框和轨迹线的帧到文件夹&#10;                if count % 30 == 0:  # 每30帧保存一次，避免保存过多图片&#10;                    try:&#10;                        # 创建保存目录&#10;                        save_dir = os.path.join(self.result_path, &quot;detection_frames&quot;)&#10;                        if not os.path.exists(save_dir):&#10;                            os.makedirs(save_dir)&#10;                            logging.info(f&quot;Created detection frames directory: {save_dir}&quot;)&#10;                        &#10;                        # 生成文件名&#10;                        timestamp = time.strftime(&quot;%Y%m%d_%H%M%S&quot;)&#10;                        frame_filename = f&quot;detection_frame_{timestamp}_{count}.jpg&quot;&#10;                        frame_path = os.path.join(save_dir, frame_filename)&#10;                        &#10;                        # 保存图片&#10;                        cv2.imwrite(frame_path, display_frame)&#10;                        &#10;                        logging.info(f&quot;Detection frame saved: {frame_path}&quot;)&#10;                    except Exception as e:&#10;                        logging.error(f&quot;Failed to save detection frame: {e}&quot;)&#10;                &#10;                &#10;                self.dataT = time.time() - t1&#10;    &#10;                if count % 100 == 99:&#10;                    logging.info(f&quot;Inference time: {self.dataT:.4f}s&quot;)&#10;                    count = 0&#10;                count += 1&#10;        except Exception as e:&#10;            logging.error(f&quot;Frame processing error: {e}&quot;)&#10;&#10;        &#10;    def save_alert_video(self, alarm_level, alarmClass):&#10;        if not self.alert_frames:&#10;            return&#10;        &#10;        # 创建以 video_name 命名的文件夹&#10;        alert_video_dir = os.path.join(self.result_path, self.video_name)&#10;        &#10;        if not os.path.exists(alert_video_dir):&#10;            os.makedirs(alert_video_dir)&#10;            logging.info(f&quot;Created folder: {alert_video_dir}&quot;)&#10;        &#10;        # 生成时间戳&#10;        timestamp = time.strftime(&quot;%Y%m%d-%H%M%S&quot;)&#10;        &#10;        # 告警视频路径&#10;        alert_video_path = os.path.join(&#10;            alert_video_dir, &#10;            f&quot;alert_{self.video_name}_{timestamp}.mp4&quot;&#10;        )&#10;        &#10;        # 告警图片路径&#10;        alert_image_path = os.path.join(&#10;            alert_video_dir, &#10;            f&quot;alert_{self.video_name}_{timestamp}.jpg&quot;&#10;        )&#10;        &#10;        # 保存告警图片&#10;        try:&#10;            cv2.imwrite(alert_image_path, self.alert_frames[-1])  # 保存告警图片&#10;            logging.info(f&quot;Alert image saved successfully: {alert_image_path}&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;Failed to save alert image: {e}&quot;)&#10;        &#10;        # 保存告警视频&#10;        threading.Thread(&#10;            target=self.write_alert_video, &#10;            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)&#10;        ).start()&#10;        &#10;        # 存储告警信息&#10;        self.alert_info = {&#10;            &quot;alert_image_path&quot;: alert_image_path,&#10;            &quot;alert_video_path&quot;: alert_video_path,&#10;            &quot;full_video_path&quot;: os.path.join(self.result_path, f&quot;{self.video_name}.mp4&quot;),&#10;            &quot;stream_url&quot;: self.dict_json[&quot;droneStreamUrl&quot;]&#10;        }&#10;        logging.info(f&quot;Alert info: {json.dumps(self.alert_info)}&quot;)&#10;        &#10;        # 调用 minio_update 和 alarm_info_post&#10;        # self.call_minio_and_alarm(alert_image_path, alert_video_path)&#10;        &#10;    &#10;    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):&#10;        # 调用 minio_update&#10;        logging.info(f&quot;Uploading image to MinIO: {alert_image_path}&quot;)&#10;        return_image_name = minio_update.minio_interface(&#10;            self.dict_json, &quot;alarm&quot;, os.path.basename(alert_image_path), alert_image_path&#10;        )&#10;        logging.info(f&quot;Image uploaded to MinIO: {return_image_name}&quot;)&#10;        &#10;        logging.info(f&quot;Uploading video to MinIO: {alert_video_path}&quot;)&#10;        return_video_name = minio_update.minio_interface(&#10;            self.dict_json, &quot;clip&quot;, os.path.basename(alert_video_path), alert_video_path&#10;        )&#10;        logging.info(f&quot;Video uploaded to MinIO: {return_video_name}&quot;)&#10;        &#10;        # 调用 alarm_info_post&#10;        logging.info(f&quot;Posting alarm info to server&quot;)&#10;        &#10;        self.drone_state = drone_state_get(self.dict_json)&#10;&#10;        drones_server.alarm_info_post(&#10;            active_drone=self.dict_json,&#10;            drone_state=self.drone_state,&#10;            classes=alarmClass,  # 告警类别&#10;            alarmLevel=alarm_level,  # 告警级别&#10;            alarmImageUrl=return_image_name,  # 告警图片路径&#10;            videoUrl=return_video_name  # 告警视频路径&#10;        )&#10;        logging.info(f&quot;Alarm info posted to server&quot;)&#10;&#10;    # @staticmethod&#10;    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):&#10;        command = [&#10;            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',&#10;            '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',&#10;            '-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-preset', 'fast',&#10;            '-crf', '23', alert_video_path  # 使用软件编码器libx264&#10;        ]&#10;        try:&#10;            pipe = subprocess.Popen(command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)&#10;            for frame in frames:&#10;                pipe.stdin.write(frame.tobytes())&#10;            pipe.stdin.close()&#10;            pipe.wait()&#10;            logging.info(f&quot;Alert video saved: {alert_video_path}&quot;)&#10;            &#10;            # 异步调用 minio_update.minio_interface&#10;            threading.Thread(&#10;                target=self.call_minio_and_alarm, &#10;                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)&#10;            ).start()&#10;        except Exception as e:&#10;            logging.error(f&quot;Failed to save alert video: {e}&quot;)   &#10;&#10;                &#10;                &#10;    def pushImg(self):&#10;        try:&#10;            frame_count = 0  # 帧计数器&#10;            frame_interval = 1 / 30  # 目标帧率 30fps，每帧间隔 0.033 秒&#10;            last_frame_time = time.time()  # 记录上一帧的时间&#10;            logging.info(f'推流线程启动')&#10;            while self.is_switchOn:&#10;                t_start = time.time()&#10;&#10;                # 优先从push_queue获取已处理的帧（带检测框和轨迹线）&#10;                if not self.push_queue.empty():&#10;                    frame = self.push_queue.get()&#10;                    logging.info(f'从push_queue获取帧进行推流')&#10;                else:&#10;                    time.sleep(0.001)  # 避免空队列时 CPU 占用过高&#10;                    continue&#10;                &#10;                # 推流&#10;                try:&#10;                    # 检查管道是否仍然有效&#10;                    if self.pipe.poll() is not None:&#10;                        logging.error(&quot;FFmpeg process has exited, stopping push&quot;)&#10;                        break&#10;                    &#10;                    self.pipe.stdin.write(frame.tobytes())&#10;                    if frame_count % 100 == 0:&#10;                        logging.info(f&quot;Frame {frame_count} successfully pushed to stream&quot;)&#10;                except BrokenPipeError:&#10;                    logging.error(&quot;Broken pipe detected, FFmpeg process may have crashed&quot;)&#10;                    break&#10;                except Exception as e:&#10;                    logging.error(f&quot;Failed to write frame to pipe: {e}&quot;)&#10;                    break&#10;                &#10;                # 计算处理时间&#10;                elapsed = time.time() - t_start&#10;                frame_count += 1&#10;                &#10;                # 每100帧打印一次平均处理时间&#10;                if frame_count % 100 == 0:&#10;                    logging.info(f&quot;push time =: {elapsed}&quot;)&#10;                &#10;                # 精确帧率控制&#10;                next_frame_time = last_frame_time + frame_interval&#10;                sleep_time = max(0.0, next_frame_time - time.time())&#10;                time.sleep(sleep_time)&#10;                &#10;                final_push_time = time.time() -  t_start                 &#10;                if frame_count % 100 == 0:&#10;                    logging.info(f&quot;final time =: {final_push_time}&quot;)&#10;                &#10;        except Exception as e:&#10;            logging.error(f&quot;Streaming error: {e}&quot;)&#10;        finally:&#10;            try:&#10;                self.pipe.stdin.close()&#10;                self.pipe.wait()&#10;            except Exception as e:&#10;                logging.error(f&quot;Failed to close pipe: {e}&quot;)&#10;&#10;                &#10;&#10;    def saveImg(self):&#10;        try:&#10;            while self.is_switchOn or not self.save_queue.empty():&#10;                if not self.save_queue.empty():&#10;                    frame = self.save_queue.get()&#10;                    try:&#10;                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())&#10;                    except Exception as e:&#10;                        logging.error(f&quot;Failed to write frame to save video: {e}&quot;)&#10;                        break&#10;                else:&#10;                    time.sleep(0.01)  # 避免空队列时 CPU 占用过高&#10;    &#10;            # 视频保存完成后，上传到 MinIO&#10;            if os.path.exists(self.save_full_video_path):&#10;                return_video_name = minio_update.minio_interface(&#10;                    self.dict_json, &#10;                    &quot;full&quot;, &#10;                    os.path.basename(self.save_full_video_path), &#10;                    self.save_full_video_path&#10;                )&#10;                logging.info(f&quot;Full video uploaded to MinIO: {return_video_name}&quot;)&#10;            else:&#10;                logging.error(f&quot;Full video file not found: {self.save_full_video_path}&quot;)&#10;    &#10;        except Exception as e:&#10;            logging.error(f&quot;Video save error: {e}&quot;)&#10;        finally:&#10;            self.ffmpegSaveVideo.stdin.close()&#10;            self.ffmpegSaveVideo.wait()&#10;            logging.info(&quot;Video save process completed.&quot;)&#10;&#10;        &#10;    def set_switch_on(self, listenVule):&#10;        self.is_switchOn = listenVule&#10;        &#10;    def set_video_name(self, video_name):&#10;        self.video_name = video_name&#10;&#10;    def set_mission_id(self, mission_id):&#10;        self.mission_id = mission_id&#10;&#10;    def _listen_for_stop(self):&#10;        while self.is_switchOn:&#10;            user_input = input(&quot;输入 'stop' 关闭视频流: &quot;)&#10;            if user_input.strip().lower() == 'stop':&#10;                self.is_switchOn = False&#10;                logging.info(&quot;收到关闭信号，正在关闭视频流...&quot;)&#10;                break&#10;            &#10;    def listen_for_alert(self):&#10;        while self.is_switchOn:&#10;            if self.alert_flag:&#10;                # 当 alert_flag 为 True 时执行特定操作&#10;                self.handle_alert()&#10;                self.alert_flag = False  # 重置 alert_flag&#10;            time.sleep(0.1)  # 每 0.1 秒检查一次&#10;    def handle_alert(self):&#10;        # 在这里实现当 alert_flag 为 True 时需要执行的操作&#10;        logging.info(&quot;Alert detected! Handling alert...&quot;)&#10;        self.drone_state = drone_state_get(self.dict_json)&#10;                &#10;    &#10;    # 原有的轨迹线绘制相关方法已被trajectory_interface.py替代&#10;&#10;    def startThread1(self, input_stream, output_stream, saveVideoPath):&#10;        try:&#10;            self.open_ffmpeg_process(output_stream)&#10;            self.openFfmpegSaveVideo(saveVideoPath)&#10;            threads = [&#10;                threading.Thread(target=self.read_video, args=(input_stream,)),&#10;                threading.Thread(target=self.process_frame),&#10;                threading.Thread(target=self.pushImg),&#10;                threading.Thread(target=self.saveImg)&#10;            ]&#10;            for t in threads:&#10;                t.daemon = True&#10;                t.start()&#10;            for t in threads:&#10;                t.join()&#10;        except Exception as e:&#10;            logging.error(f&quot;Thread start failed: {e}&quot;)&#10;            &#10;    def check_pipe_health(self):&#10;        while self.is_switchOn:&#10;            if self.pipe.poll() is not None:  # 检查 FFmpeg 进程是否已退出&#10;                logging.error(&quot;FFmpeg process has exited unexpectedly.&quot;)&#10;                self.is_switchOn = False&#10;                break&#10;            time.sleep(5)  # 每5秒检查一次&#10;            &#10;    def startThread(self, input_stream, output_stream, saveVideoPath):&#10;        try:&#10;            self.open_ffmpeg_process(output_stream)&#10;            self.openFfmpegSaveVideo(saveVideoPath)&#10;&#10;            # 创建线程并添加到管理列表&#10;            threads = [&#10;                threading.Thread(target=self.read_video, args=(input_stream,), name=&quot;read_video&quot;),&#10;                threading.Thread(target=self.process_frame, name=&quot;process_frame&quot;),&#10;                threading.Thread(target=self.pushImg, name=&quot;pushImg&quot;),&#10;                threading.Thread(target=self.saveImg, name=&quot;saveImg&quot;),&#10;                threading.Thread(target=self.check_pipe_health, name=&quot;check_pipe_health&quot;)&#10;            ]&#10;&#10;            # 设置线程为daemon并启动&#10;            for t in threads:&#10;                t.daemon = True&#10;                self.threads.append(t)&#10;                t.start()&#10;&#10;            # 等待所有线程完成&#10;            for t in threads:&#10;                t.join()&#10;&#10;        except KeyboardInterrupt:&#10;            logging.info(&quot;收到键盘中断信号，正在停止线程...&quot;)&#10;            self.is_switchOn = False&#10;        except Exception as e:&#10;            logging.error(f&quot;线程启动失败: {e}&quot;)&#10;        finally:&#10;            # 确保资源被清理&#10;            self.cleanup_resources()&#10;&#10;    def cleanup_resources(self):&#10;        &quot;&quot;&quot;清理所有资源&quot;&quot;&quot;&#10;        if self.cleanup_done:&#10;            return&#10;&#10;        logging.info(&quot;开始清理资源...&quot;)&#10;&#10;        # 1. 设置停止标志&#10;        self.is_switchOn = False&#10;&#10;        # 2. 清理FFmpeg进程&#10;        try:&#10;            if hasattr(self, 'pipe') and self.pipe:&#10;                if self.pipe.poll() is None:  # 进程仍在运行&#10;                    self.pipe.stdin.close()&#10;                    self.pipe.terminate()&#10;                    self.pipe.wait(timeout=5)&#10;                logging.info(&quot;FFmpeg推流进程已关闭&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;关闭FFmpeg推流进程时出错: {e}&quot;)&#10;            try:&#10;                if hasattr(self, 'pipe') and self.pipe:&#10;                    self.pipe.kill()&#10;            except:&#10;                pass&#10;&#10;        try:&#10;            if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:&#10;                if self.ffmpegSaveVideo.poll() is None:&#10;                    self.ffmpegSaveVideo.stdin.close()&#10;                    self.ffmpegSaveVideo.terminate()&#10;                    self.ffmpegSaveVideo.wait(timeout=5)&#10;                logging.info(&quot;FFmpeg保存进程已关闭&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;关闭FFmpeg保存进程时出错: {e}&quot;)&#10;            try:&#10;                if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:&#10;                    self.ffmpegSaveVideo.kill()&#10;            except:&#10;                pass&#10;&#10;        # 3. 等待所有线程结束&#10;        for thread in self.threads:&#10;            if thread.is_alive():&#10;                try:&#10;                    thread.join(timeout=2.0)&#10;                    if thread.is_alive():&#10;                        logging.warning(f&quot;线程 {thread.name} 超时未结束&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;等待线程结束时出错: {e}&quot;)&#10;&#10;        # 4. 清空所有队列&#10;        try:&#10;            while not self.frame_queue.empty():&#10;                self.frame_queue.get_nowait()&#10;            while not self.push_queue.empty():&#10;                self.push_queue.get_nowait()&#10;            while not self.save_queue.empty():&#10;                self.save_queue.get_nowait()&#10;            logging.info(&quot;所有队列已清空&quot;)&#10;        except Exception as e:&#10;            logging.error(f&quot;清空队列时出错: {e}&quot;)&#10;&#10;        self.cleanup_done = True&#10;        logging.info(&quot;资源清理完成&quot;)&#10;&#10;class StreamManager:&#10;    def __init__(self, model_path):&#10;        self.model_path = model_path&#10;        self.streams = []&#10;        self.lock = multiprocessing.Lock()&#10;        self.processor = VideoStreamProcessor(model_path)&#10;        self.active_processes = {}&#10;        self.cleanup_done = False&#10;&#10;        # 注册信号处理器和清理函数&#10;        signal.signal(signal.SIGINT, self.signal_handler)&#10;        signal.signal(signal.SIGTERM, self.signal_handler)&#10;        atexit.register(self.cleanup_all_resources)&#10;&#10;    def signal_handler(self, signum, frame):&#10;        &quot;&quot;&quot;处理系统信号&quot;&quot;&quot;&#10;        logging.info(f&quot;收到信号 {signum}，开始清理所有资源...&quot;)&#10;        self.cleanup_all_resources()&#10;&#10;    def cleanup_all_resources(self):&#10;        &quot;&quot;&quot;清理所有流管理器的资源&quot;&quot;&quot;&#10;        if self.cleanup_done:&#10;            return&#10;&#10;        logging.info(&quot;开始清理StreamManager资源...&quot;)&#10;&#10;        with self.lock:&#10;            # 停止所有活动进程&#10;            for input_path, process in list(self.active_processes.items()):&#10;                try:&#10;                    if process.is_alive():&#10;                        logging.info(f&quot;正在停止进程: {input_path}&quot;)&#10;                        self.processor.stop_stream(input_path)&#10;                        process.terminate()&#10;                        process.join(timeout=5)&#10;                        if process.is_alive():&#10;                            logging.warning(f&quot;进程 {input_path} 超时未结束，强制杀死&quot;)&#10;                            process.kill()&#10;                            process.join()&#10;                        logging.info(f&quot;进程 {input_path} 已停止&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;停止进程 {input_path} 时出错: {e}&quot;)&#10;&#10;            self.active_processes.clear()&#10;            self.streams.clear()&#10;&#10;        self.cleanup_done = True&#10;        logging.info(&quot;StreamManager资源清理完成&quot;)&#10;&#10;    def add_stream(self, dict_json, out_res):&#10;        input_path = dict_json[&quot;droneStreamUrl&quot;]&#10;        output_url = dict_json[&quot;aiStreamUrl&quot;]&#10;        video_name = dict_json[&quot;taskId&quot;]&#10;        &#10;        with self.lock:&#10;            # 创建保存文件夹&#10;            save_folder = os.path.join(out_res, video_name)&#10;            if not os.path.exists(save_folder):&#10;                os.makedirs(save_folder)&#10;                logging.info(f&quot;Created folder: {save_folder}&quot;)&#10;&#10;            save_path = os.path.join(save_folder, f&quot;{video_name}.mp4&quot;)&#10;&#10;            # 启动新流的处理进程&#10;            process = multiprocessing.Process(&#10;                target=self.processor.process_stream,&#10;                args=(dict_json, save_path),&#10;                name=f&quot;stream_{video_name}&quot;&#10;            )&#10;            process.start()&#10;            logging.info(f'推流进程已开启: {video_name}')&#10;&#10;            # 存储进程信息&#10;            self.active_processes[input_path] = process&#10;            self.streams.append({&#10;                &quot;input&quot;: input_path,&#10;                &quot;output&quot;: output_url,&#10;                &quot;save_path&quot;: save_path,&#10;                &quot;video_name&quot;: video_name&#10;            })&#10;            logging.info(f&quot;Stream {output_url} has been added and started.&quot;)&#10;&#10;    def stop_stream(self, input_url):&#10;        with self.lock:&#10;            if input_url in self.active_processes:&#10;                process = self.active_processes[input_url]&#10;                try:&#10;                    # 设置流的开关为 False&#10;                    self.processor.stop_stream(input_url)&#10;&#10;                    # 等待进程正常结束&#10;                    process.join(timeout=10)&#10;&#10;                    # 如果进程仍在运行，强制终止&#10;                    if process.is_alive():&#10;                        logging.warning(f&quot;进程 {input_url} 超时未结束，强制终止&quot;)&#10;                        process.terminate()&#10;                        process.join(timeout=5)&#10;&#10;                        if process.is_alive():&#10;                            process.kill()&#10;                            process.join()&#10;&#10;                    del self.active_processes[input_url]&#10;&#10;                    # 从流列表中移除&#10;                    self.streams = [s for s in self.streams if s[&quot;input&quot;] != input_url]&#10;&#10;                    logging.info(f&quot;Stream {input_url} has been stopped.&quot;)&#10;                except Exception as e:&#10;                    logging.error(f&quot;停止流 {input_url} 时出错: {e}&quot;)&#10;            else:&#10;                logging.warning(f&quot;Stream {input_url} not found.&quot;)&#10;&#10;    def get_streams(self):&#10;        with self.lock:&#10;            return self.streams&#10;&#10;    def is_stream_active(self, input_url):&#10;        with self.lock:&#10;            return input_url in self.active_processes and self.active_processes[input_url].is_alive()&#10;&#10;class VideoStreamProcessor:&#10;&#10;    def __init__(self, model_path):&#10;        self.model_path = model_path&#10;        self.active_detectors = {}  # 用于存储活动的检测器&#10;        self.lock = multiprocessing.Lock()  # 用于进程安全的操作&#10;&#10;    def process_stream(self, dict_json, save_path):&#10;        input_path = dict_json[&quot;droneStreamUrl&quot;]&#10;        output_url = dict_json[&quot;aiStreamUrl&quot;]&#10;        video_name = dict_json[&quot;taskId&quot;]&#10;&#10;        detector = None&#10;        try:&#10;            detector = multiDealImg(self.model_path, save_path, dict_json)&#10;            detector.set_switch_on(True)&#10;            detector.set_video_name(video_name)&#10;&#10;            with self.lock:&#10;                self.active_detectors[input_path] = detector&#10;&#10;            detector.startThread(input_path, output_url, save_path)&#10;&#10;        except Exception as e:&#10;            logging.error(f&quot;处理流 {input_path} 时出错: {e}&quot;)&#10;        finally:&#10;            # 确保资源被清理&#10;            if detector:&#10;                detector.cleanup_resources()&#10;&#10;            with self.lock:&#10;                if input_path in self.active_detectors:&#10;                    del self.active_detectors[input_path]&#10;            logging.info(f&quot;Stream {input_path} 处理完成并清理资源&quot;)&#10;&#10;    def stop_stream(self, input_path):&#10;        with self.lock:&#10;            if input_path in self.active_detectors:&#10;                detector = self.active_detectors[input_path]&#10;                detector.set_switch_on(False)&#10;                detector.cleanup_resources()&#10;                logging.info(f&quot;Detector for stream {input_path} has been stopped.&quot;)&#10;            else:&#10;                logging.warning(f&quot;Detector for stream {input_path} not found.&quot;)&#10;&#10;    def is_detector_active(self, input_path):&#10;        with self.lock:&#10;            return input_path in self.active_detectors&#10;&#10;# 定义比较函数，基于指定的关键字&#10;def is_equal(dict1, dict2, keys):&#10;    return all(dict1.get(key) == dict2.get(key) for key in keys)&#10;&#10;# 添加全局清理函数&#10;def setup_global_cleanup():&#10;    &quot;&quot;&quot;设置全局资源清理&quot;&quot;&quot;&#10;    def cleanup_handler(signum, frame):&#10;        logging.info(&quot;收到退出信号，正在清理全局资源...&quot;)&#10;        # 强制清理所有资源并退出&#10;        try:&#10;            # 获取当前进程的所有子进程&#10;            current_process = psutil.Process()&#10;            for child in current_process.children(recursive=True):&#10;                try:&#10;                    child.terminate()&#10;                    child.wait(timeout=3)&#10;                    if child.is_running():&#10;                        child.kill()&#10;                except (psutil.NoSuchProcess, psutil.TimeoutExpired):&#10;                    pass&#10;        except Exception as e:&#10;            logging.error(f&quot;清理子进程时出错: {e}&quot;)&#10;&#10;        logging.info(&quot;全局资源清理完成&quot;)&#10;        os._exit(0)&#10;&#10;    signal.signal(signal.SIGINT, cleanup_handler)&#10;    signal.signal(signal.SIGTERM, cleanup_handler)&#10;&#10;if __name__ == &quot;__main__&quot;:&#10;    # 设置全局清理&#10;    setup_global_cleanup()&#10;&#10;    setup_logging()&#10;&#10;    # model_path = &quot;./model/best0701_640_2.pt&quot;&#10;    model_path = &quot;/root/ultralytics-main/model/best_cj.pt&quot;&#10;    out_res = &quot;./res&quot;&#10;    stream_manager = StreamManager(model_path)&#10;&#10;    # 定义关键字列表&#10;    keys = [&quot;droneStreamUrl&quot;]&#10;&#10;    presentStream = []&#10;&#10;    try:&#10;        while True:&#10;            # 获取token&#10;            login_token_get()&#10;            # 获取正在飞行任务中的无人机列表&#10;            active_drones_list = active_drones_get()&#10;            # 获取 active_drones_list 中有且 presentStream 中没有的元素&#10;            drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]&#10;            print(drones_unique)&#10;&#10;            if(len(drones_unique) &gt; 0):&#10;                for active_drone in drones_unique:&#10;                    print(&quot;现在执行active_drone的for循环&quot;)&#10;                    stream_manager.add_stream(active_drone, out_res)&#10;            else:&#10;                print(presentStream)&#10;                # print('111111111111111未检测')&#10;&#10;            presentStream = active_drones_list&#10;            time.sleep(15)&#10;&#10;    except KeyboardInterrupt:&#10;        logging.info(&quot;收到键盘中断，正在关闭程序...&quot;)&#10;    except Exception as e:&#10;        logging.error(f&quot;主程序运行出错: {e}&quot;)&#10;    finally:&#10;        # 确保清理所有资源&#10;        stream_manager.cleanup_all_resources()&#10;        logging.info(&quot;程序正常退出&quot;)" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>