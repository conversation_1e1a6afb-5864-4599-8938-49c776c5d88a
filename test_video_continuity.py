# -*- coding: utf-8 -*-
"""
视频回闪问题测试脚本
专门用于诊断和测试视频播放连续性问题
"""

import cv2
import time
import queue
import threading
import numpy as np
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_continuity_test.log'),
        logging.StreamHandler()
    ]
)

class VideoContinuityTester:
    def __init__(self, video_path, output_window_name="Video Test"):
        self.video_path = video_path
        self.output_window_name = output_window_name
        self.is_running = False
        
        # 不同的队列测试配置
        self.test_configs = {
            "原始LIFO": {
                "queue_type": "LifoQueue",
                "maxsize": 3,
                "frame_interval": 1/30
            },
            "优化FIFO": {
                "queue_type": "Queue", 
                "maxsize": 2,
                "frame_interval": 1/25
            },
            "大缓冲FIFO": {
                "queue_type": "Queue",
                "maxsize": 5,
                "frame_interval": 1/25
            }
        }
        
    def test_queue_behavior(self, config_name, duration=10):
        """测试不同队列配置的行为"""
        print(f"\n=== 测试配置: {config_name} ===")
        config = self.test_configs[config_name]
        
        # 创建队列
        if config["queue_type"] == "LifoQueue":
            test_queue = queue.LifoQueue(maxsize=config["maxsize"])
        else:
            test_queue = queue.Queue(maxsize=config["maxsize"])
            
        self.is_running = True
        frame_count = 0
        dropped_frames = 0
        
        # 统计信息
        frame_times = []
        last_frame_time = time.time()
        
        def read_frames():
            nonlocal frame_count, dropped_frames
            cap = cv2.VideoCapture(self.video_path)
            if not cap.isOpened():
                logging.error(f"无法打开视频: {self.video_path}")
                return
                
            while self.is_running:
                ret, frame = cap.read()
                if not ret:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 循环播放
                    continue
                    
                frame = cv2.resize(frame, (1920, 1080))
                
                # 添加帧编号标记
                cv2.putText(frame, f"Frame: {frame_count}", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, f"Config: {config_name}", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
                
                # 尝试放入队列
                try:
                    test_queue.put_nowait(frame)
                    frame_count += 1
                except queue.Full:
                    dropped_frames += 1
                    # 测试不同的队列满处理策略
                    try:
                        test_queue.get_nowait()  # 丢弃旧帧
                        test_queue.put_nowait(frame)
                        frame_count += 1
                    except queue.Empty:
                        pass
                        
            cap.release()
        
        def display_frames():
            nonlocal last_frame_time
            target_interval = config["frame_interval"]
            next_display_time = time.time()
            
            while self.is_running:
                current_time = time.time()
                
                # 时间控制
                if current_time < next_display_time:
                    time.sleep(0.001)
                    continue
                    
                try:
                    frame = test_queue.get_nowait()
                    
                    # 计算帧间隔
                    frame_interval = current_time - last_frame_time
                    frame_times.append(frame_interval)
                    last_frame_time = current_time
                    
                    # 显示帧间隔信息
                    if len(frame_times) > 1:
                        avg_interval = np.mean(frame_times[-10:])  # 最近10帧平均
                        cv2.putText(frame, f"Interval: {avg_interval*1000:.1f}ms", (50, 150), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
                    
                    cv2.imshow(self.output_window_name, frame)
                    
                    # 按ESC退出
                    if cv2.waitKey(1) & 0xFF == 27:
                        self.is_running = False
                        break
                        
                    next_display_time = current_time + target_interval
                    
                except queue.Empty:
                    time.sleep(0.001)
                    continue
        
        # 启动线程
        read_thread = threading.Thread(target=read_frames)
        display_thread = threading.Thread(target=display_frames)
        
        read_thread.daemon = True
        display_thread.daemon = True
        
        read_thread.start()
        display_thread.start()
        
        # 运行指定时间
        time.sleep(duration)
        self.is_running = False
        
        # 等待线程结束
        read_thread.join(timeout=2)
        display_thread.join(timeout=2)
        
        # 统计结果
        if frame_times:
            avg_interval = np.mean(frame_times)
            std_interval = np.std(frame_times)
            min_interval = np.min(frame_times)
            max_interval = np.max(frame_times)
            
            print(f"总帧数: {frame_count}")
            print(f"丢帧数: {dropped_frames}")
            print(f"丢帧率: {dropped_frames/(frame_count+dropped_frames)*100:.1f}%")
            print(f"平均帧间隔: {avg_interval*1000:.2f}ms")
            print(f"帧间隔标准差: {std_interval*1000:.2f}ms")
            print(f"最小间隔: {min_interval*1000:.2f}ms")
            print(f"最大间隔: {max_interval*1000:.2f}ms")
            
            # 检测回闪（帧间隔异常）
            threshold = avg_interval + 2 * std_interval
            abnormal_intervals = [t for t in frame_times if t > threshold]
            if abnormal_intervals:
                print(f"⚠️  检测到 {len(abnormal_intervals)} 次异常间隔，可能导致回闪")
            else:
                print("✅ 未检测到明显的帧间隔异常")
        
        cv2.destroyAllWindows()
        return {
            "frame_count": frame_count,
            "dropped_frames": dropped_frames,
            "avg_interval": avg_interval if frame_times else 0,
            "std_interval": std_interval if frame_times else 0
        }

    def run_all_tests(self, duration=15):
        """运行所有测试配置"""
        print("开始视频连续性测试")
        print(f"测试视频: {self.video_path}")
        print(f"每个配置测试时长: {duration}秒")
        print("按ESC可提前退出当前测试")
        print("-" * 60)
        
        results = {}
        for config_name in self.test_configs:
            result = self.test_queue_behavior(config_name, duration)
            results[config_name] = result
            
            print(f"{config_name} 测试完成")
            print("等待3秒后开始下一个测试...")
            time.sleep(3)
        
        # 对比结果
        print("\n" + "="*60)
        print("测试结果对比")
        print("="*60)
        
        print(f"{'配置':<15} {'帧数':<8} {'丢帧率':<8} {'平均间隔':<12} {'间隔稳定性':<12}")
        print("-" * 60)
        
        for config_name, result in results.items():
            total_frames = result['frame_count'] + result['dropped_frames']
            drop_rate = result['dropped_frames'] / total_frames * 100 if total_frames > 0 else 0
            avg_ms = result['avg_interval'] * 1000 if result['avg_interval'] else 0
            stability = result['std_interval'] * 1000 if result['std_interval'] else 0
            
            print(f"{config_name:<15} {result['frame_count']:<8} {drop_rate:<8.1f}% {avg_ms:<12.1f}ms {stability:<12.1f}ms")
        
        # 推荐配置
        print("\n推荐:")
        best_config = min(results.keys(), 
                         key=lambda k: results[k]['std_interval'] if results[k]['std_interval'] else float('inf'))
        print(f"最稳定的配置: {best_config}")

def main():
    # 设置测试视频路径
    video_path = r"D:\crscu\object_detection\result3.mp4"  # 请替换为你的测试视频路径
    
    # 检查视频文件
    import os
    if not os.path.exists(video_path):
        print(f"错误: 找不到视频文件 {video_path}")
        print("请将测试视频命名为 test_video.mp4 并放在当前目录")
        return
    
    # 创建测试器并运行测试
    tester = VideoContinuityTester(video_path)
    tester.run_all_tests(duration=10)  # 每个配置测试10秒

if __name__ == "__main__":
    main()
