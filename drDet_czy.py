# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS
"""

#基于test10,将多线程改为多进程
import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque

from ultralytics import YOLO
import subprocess
# 同时从 plotting 模块导入所有三个
from ultralytics.utils.plotting import Annotator, colors, save_one_box
import logging
import json

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math

import sys
from apscheduler.schedulers.blocking import BlockingScheduler

# 导入轨迹线接口
from trajectory_interface import TrajectoryRenderer


logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class multiDealImg(object):
    model = None  # 类变量，共享模型
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, model_path, save_path, dict_json):
        if multiDealImg.model is None:
            multiDealImg.model = YOLO(model_path).to('cuda')
        self.model = multiDealImg.model
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json  # 存储 initial_streams
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化队列大小，减少内存占用和延迟
        self.save_queue = queue.Queue(maxsize=5)  # 减小保存队列
        self.push_queue = queue.Queue(maxsize=2)  # 改为FIFO，减小推流队列
        self.new_queue = queue.Queue(maxsize=1)   # 改为FIFO
        self.frame_queue = queue.Queue(maxsize=2)  # 增加帧队列，避免丢帧
        
        # 添加推理控制参数，提高FPS
        self.inference_interval = 2  # 每2帧进行一次推理
        self.frame_counter = 0
        self.last_results = None  # 缓存推理结果

        # self.names = ["HelAndRef", "Ref", "excavator", "NoHelAndRef", "Hel"]
        self.names = ["excavator", "crane", "compactor", "tank truck", "loader"]
        self.results = None
        self.dataT = 2
        

        # 告警相关
        self.buffer = deque(maxlen=125)  # 5秒缓存（25fps）
        self.alert_flag = False
        self.alert_frames = []
        self.alert_counter = 0
        self.alert_lock = threading.Lock()
        self.alert_info = {}  # 存储告警信息
        
        # self.alert_listener_thread = threading.Thread(target=self.listen_for_alert)
        # self.alert_listener_thread.daemon = True
        # self.alert_listener_thread.start()
        
        self.drone_state = {}
        
        self.drone_angle = 0
        # self.drone_angle = self.dict_json["attitude_head"]
        
        # 轨迹线动态参数设置（可手动调整）
        self.trajectory_length_multiplier = 2.0  # 轨迹线长度为视频高度的倍数
        self.trajectory_width_ratio = 0.5  # 两条轨迹线间距为视频宽度的比例
        
        # 手动轨迹线参数（设置为None时使用动态计算）
        self.manual_trajectory_length = None  # 手动设置的轨迹线长度（像素）
        self.manual_trajectory_width = None   # 手动设置的轨迹线宽度（像素）
        
        # 初始化轨迹线渲染器，启用动态尺寸调整
        self.trajectory_renderer = TrajectoryRenderer(
            enable_dynamic_sizing=True,
            length_multiplier=self.trajectory_length_multiplier,
            width_ratio=self.trajectory_width_ratio
        )
        
        # 设置手动参数（如果有的话）
        if self.manual_trajectory_length is not None or self.manual_trajectory_width is not None:
            self.trajectory_renderer.set_manual_params(
                length=self.manual_trajectory_length,
                width=self.manual_trajectory_width
            )
        
        # # 模拟航向角变化相关参数
        # self.simulated_heading = 0.0  # 当前模拟航向角
        # self.heading_direction = 1  # 航向角变化方向 (1为正向，-1为反向)
        # self.heading_change_rate = 5  # 航向角变化速率（度/帧）
        # self.heading_min = -180  # 最小航向角
        # self.heading_max = 180  # 最大航向角
        # self.frame_count = 0      # 帧计数器
        
    # def update_simulated_heading(self):
    #     """
    #     更新模拟航向角，实现在-90到90度之间的往复变化
    #     """
    #     # 更新航向角
    #     self.simulated_heading += self.heading_direction * self.heading_change_rate
        
        # 检查边界并改变方向
        # if self.simulated_heading >= self.heading_max:
        #     self.simulated_heading = self.heading_max
        #     self.heading_direction = -1  # 改为负方向
        # elif self.simulated_heading <= self.heading_min:
        #     self.simulated_heading = self.heading_min
        #     self.heading_direction = 1   # 改为正方向
        
        # return self.simulated_heading
        
        
    def open_ffmpeg_process(self, output_stream):
        # 优化FFmpeg参数，确保流畅推流，避免回闪
        self.command = [
            'ffmpeg',
            '-y',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1080),
            '-r', '25',  # 固定帧率25fps
            '-i', '-',
            '-c:v', 'h264_nvenc',
            '-preset', 'ultrafast',  # 最快编码
            '-tune', 'zerolatency',  # 零延迟调优
            '-bf', '0',  # 禁用B帧，减少延迟
            '-g', '25',  # GOP大小=帧率，每秒一个I帧
            '-keyint_min', '25',  # 最小关键帧间隔
            '-sc_threshold', '0',  # 禁用场景检测
            '-b:v', '800k',  # 码率
            '-maxrate', '1000k',  # 最大码率
            '-bufsize', '800k',  # 缓冲区大小=码率，避免缓冲
            '-r', '25',  # 输出帧率
            '-pix_fmt', 'yuv420p',
            '-crf', '28',  # 质量参数
            '-fflags', '+genpts',  # 生成时间戳
            '-fps_mode', 'cfr',  # 恒定帧率模式
            '-f', 'flv',
            output_stream
        ]
        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, bufsize=0)

    def openFfmpegSaveVideo(self, outputSaveVideo):
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1080", "-r", "30", "-i", "-",
            "-c:v", "h264_nvenc", "-pix_fmt", "yuv420p", "-crf", "23", "-preset", "fast", outputSaveVideo
        ]
        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE)

    def read_video(self, video_path):
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
    
        # 减少视频缓冲，但保持适度缓冲避免卡顿
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 2)
    
        try:
            count = 0
            frame_skip_counter = 0  # 跳帧计数器
            
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:  # 视频结束或读取失败
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                # 检查帧是否为空
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
                    
                # 改进的跳帧逻辑：保持时间连续性
                frame_skip_counter += 1
                
                # 调整帧大小
                try:
                    frame = cv2.resize(frame, (1920, 1080))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 所有帧都放入frame_queue，但跳帧处理放在process_frame中
                # 这样可以保证时序的连续性
                try:
                    self.frame_queue.put_nowait(frame)
                except queue.Full:
                    # 队列满时，丢弃最老的帧并插入新帧
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame)
                    except queue.Empty:
                        pass
                        
                # 减少new_queue更新频率但保持连续
                count += 1
                if count % 5 == 0:  # 每5帧更新一次航向角和轨迹
                    self.drone_angle = drone_yaw_get(self.dict_json)
                        
                # 为了保持视频流畅，new_queue也要定期更新
                if frame_skip_counter % 2 == 0:  # 每2帧更新一次new_queue
                    frame_with_trajectory = self.trajectory_renderer.render_trajectory(frame.copy(), self.drone_angle)
                    
                    try:
                        self.new_queue.put_nowait(frame_with_trajectory)
                    except queue.Full:
                        try:
                            self.new_queue.get_nowait()
                            self.new_queue.put_nowait(frame_with_trajectory)
                        except queue.Empty:
                            pass
                
                # 添加适度延迟，避免读取过快
                time.sleep(0.001)  # 1ms延迟
                
        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def process_frame(self):
        try:
            count = 0
            alarmLevel = "2"
            alarmClass = "excavator"
    
            while self.is_switchOn:
                try:
                    # 使用超时避免无限阻塞
                    frame = self.frame_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                    
                t1 = time.time()
                
                # 控制推理频率：不是每帧都推理，提高FPS
                self.frame_counter += 1
                should_infer = (self.frame_counter % self.inference_interval == 0)
                
                if should_infer:
                    # 使用较小分辨率进行推理，大幅提高速度
                    inference_frame = cv2.resize(frame, (640, 360))
                    with self.model_lock:
                        self.results = self.model.track(inference_frame, conf=0.5, show_conf=False, verbose=False, tracker='bytetrack.yaml')
                        self.last_results = self.results
                else:
                    # 使用缓存的推理结果
                    self.results = self.last_results
                    
                t2 = time.time()
    
                # 简化告警检测逻辑，减少计算量
                alert_triggered = False
                if self.results:
                    for result in self.results:
                        if result.boxes is not None and len(result.boxes) > 0:
                            for box in result.boxes:
                                cls = int(box.cls)
                            
                                # 获取框坐标（需要从640x360映射回1920x1080）
                                x1, y1, x2, y2 = map(int, box.xyxy[0])
                                # 坐标缩放
                                scale_x, scale_y = 1920 / 640, 1080 / 360
                                center_x = int((x1 + x2) / 2 * scale_x)
                                center_y = int((y1 + y2) / 2 * scale_y)
                                
                                # 简化告警判断，避免复杂的轨迹计算
                                frame_center_x = 960  # 1920 / 2
                                distance_from_center = abs(center_x - frame_center_x)
                                
                                if distance_from_center < 200:
                                    alarmLevel = "1"
                                    alert_triggered = True
                                elif distance_from_center < 400:
                                    alarmLevel = "2"  
                                    alert_triggered = True
                                else:
                                    alarmLevel = "0"
                                    
                                alarmClass = self.names[cls]
                                if alert_triggered:
                                    break
                            if alert_triggered:
                                break
    
                # 简化告警处理逻辑
                with self.alert_lock:
                    if alert_triggered and not self.alert_flag:
                        # 保存前五秒的帧（当前buffer中的所有内容）
                        self.alert_frames = list(self.buffer)
                        self.alert_flag = True
                        self.alert_counter = 0
    
                    # 缓存当前帧
                    self.buffer.append(frame.copy())
    
                    if self.alert_flag:
                        self.alert_frames.append(frame.copy())
                        self.alert_counter += 1
                        if self.alert_counter >= 125:  # 减少告警视频长度
                            self.save_alert_video(alarmLevel, alarmClass)
                            self.alert_flag = False
                            self.alert_frames = []
                            self.alert_counter = 0
    
                # 非阻塞方式放入保存队列
                try:
                    self.save_queue.put_nowait(frame.copy())
                except queue.Full:
                    pass  # 丢弃帧以避免阻塞
                
                # 创建带检测框和轨迹线的帧用于显示
                display_frame = frame.copy()
                
                # 始终绘制轨迹线，保持视频连续性
                display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)
                
                # 绘制检测框（使用最新的推理结果）
                try:
                    if self.results:
                        for result in self.results:
                            if result.boxes is not None and len(result.boxes) > 0:
                                for box in result.boxes:
                                    # 缩放坐标回原尺寸
                                    x1, y1, x2, y2 = box.xyxy[0]
                                    scale_x, scale_y = 1920 / 640, 1080 / 360
                                    x1, y1 = int(x1*scale_x), int(y1*scale_y)
                                    x2, y2 = int(x2*scale_x), int(y2*scale_y)
                                    cls = int(box.cls)
                                    conf = float(box.conf)
                                    
                                    # 手动绘制检测框
                                    cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                    label = f'{self.names[cls]} {conf:.2f}'
                                    cv2.putText(display_frame, label, (x1, y1-10), 
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                except Exception as e:
                    logging.error(f"Error plotting detection results: {e}")
                
                # 每帧都放入推流队列，确保视频连续
                try:
                    self.push_queue.put_nowait(display_frame)
                except queue.Full:
                    # 队列满时，丢弃最老的帧，确保最新帧能进入
                    try:
                        self.push_queue.get_nowait()
                        self.push_queue.put_nowait(display_frame)
                    except queue.Empty:
                        # 队列为空，直接放入
                        try:
                            self.push_queue.put_nowait(display_frame)
                        except queue.Full:
                            pass
                
                self.dataT = time.time() - t1
    
                if count % 100 == 99:
                    logging.info(f"Inference time: {self.dataT:.4f}s, Should infer: {should_infer}, Queue sizes - frame:{self.frame_queue.qsize()}, push:{self.push_queue.qsize()}")
                    count = 0
                count += 1
        except Exception as e:
            logging.error(f"Frame processing error: {e}")

        
    def save_alert_video(self, alarm_level, alarmClass):
        if not self.alert_frames:
            return
        
        # 创建以 video_name 命名的文件夹
        alert_video_dir = os.path.join(self.result_path, self.video_name)
        
        if not os.path.exists(alert_video_dir):
            os.makedirs(alert_video_dir)
            logging.info(f"Created folder: {alert_video_dir}")
        
        # 生成时间戳
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # 告警视频路径
        alert_video_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.mp4"
        )
        
        # 告警图片路径
        alert_image_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.jpg"
        )
        
        # 保存告警图片
        cv2.imwrite(alert_image_path, self.alert_frames[-1])  # 保存告警图片
        
        # 保存告警视频
        threading.Thread(
            target=self.write_alert_video, 
            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)
        ).start()
        
        # 存储告警信息
        self.alert_info = {
            "alert_image_path": alert_image_path,
            "alert_video_path": alert_video_path,
            "full_video_path": os.path.join(self.result_path, f"{self.video_name}.mp4"),
            "stream_url": self.dict_json["droneStreamUrl"]
        }
        logging.info(f"Alert info: {json.dumps(self.alert_info)}")
        
        # 调用 minio_update 和 alarm_info_post
        # self.call_minio_and_alarm(alert_image_path, alert_video_path)
        
    
    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):
        # 调用 minio_update
        logging.info(f"Uploading image to MinIO: {alert_image_path}")
        return_image_name = minio_update.minio_interface(
            self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
        )
        logging.info(f"Image uploaded to MinIO: {return_image_name}")
        
        logging.info(f"Uploading video to MinIO: {alert_video_path}")
        return_video_name = minio_update.minio_interface(
            self.dict_json, "clip", os.path.basename(alert_video_path), alert_video_path
        )
        logging.info(f"Video uploaded to MinIO: {return_video_name}")
        
        # 调用 alarm_info_post
        logging.info(f"Posting alarm info to server")
        
        self.drone_state = drone_state_get(self.dict_json)

        drones_server.alarm_info_post(
            active_drone=self.dict_json,
            drone_state=self.drone_state,
            classes=alarmClass,  # 告警类别
            alarmLevel=alarm_level,  # 告警级别
            alarmImageUrl=return_image_name,  # 告警图片路径
            videoUrl=return_video_name  # 告警视频路径
        )
        logging.info(f"Alarm info posted to server")

    # @staticmethod
    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):
        command = [
            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24', '-s', '1920x1080', '-r', '30', '-i', '-',
            '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-crf', '23', alert_video_path
        ]
        try:
            pipe = subprocess.Popen(command, stdin=subprocess.PIPE)
            for frame in frames:
                pipe.stdin.write(frame.tobytes())
            pipe.stdin.close()
            pipe.wait()
            logging.info(f"Alert video saved: {alert_video_path}")
            
            # 异步调用 minio_update.minio_interface
            threading.Thread(
                target=self.call_minio_and_alarm, 
                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)
            ).start()
        except Exception as e:
            logging.error(f"Failed to save alert video: {e}")   

                
                
    def pushImg(self):
        try:
            frame_count = 0
            target_fps = 25  # 目标帧率25fps
            frame_interval = 1.0 / target_fps
            next_frame_time = time.time()  # 下一帧应该推送的时间
            
            while self.is_switchOn:
                current_time = time.time()
                
                # 如果还没到推送时间，先等待
                if current_time < next_frame_time:
                    sleep_time = next_frame_time - current_time
                    if sleep_time > 0.001:  # 只有超过1ms才sleep
                        time.sleep(sleep_time)
                    continue
                
                # 优先从push_queue获取带检测框的帧，使用非阻塞方式
                frame = None
                try:
                    frame = self.push_queue.get_nowait()
                except queue.Empty:
                    # 如果push_queue为空，尝试从new_queue获取
                    try:
                        frame = self.new_queue.get_nowait()
                    except queue.Empty:
                        # 如果都没有帧，继续下一次循环
                        next_frame_time = current_time + 0.001  # 1ms后再检查
                        continue
                
                if frame is None:
                    continue
                
                # 推流
                try:
                    # 检查管道是否仍然有效
                    if hasattr(self, 'pipe') and self.pipe and self.pipe.poll() is not None:
                        logging.error("FFmpeg process has exited, stopping push")
                        break
                    
                    self.pipe.stdin.write(frame.tobytes())
                    self.pipe.stdin.flush()  # 立即刷新缓冲区
                except BrokenPipeError:
                    logging.error("Broken pipe detected, FFmpeg process may have crashed")
                    break
                except Exception as e:
                    logging.error(f"Failed to write frame to pipe: {e}")
                    break
                
                frame_count += 1
                
                # 每100帧打印一次状态
                if frame_count % 100 == 0:
                    queue_info = f"push:{self.push_queue.qsize()}, new:{self.new_queue.qsize()}"
                    logging.info(f"Push frame {frame_count}, Queue: {queue_info}")
                
                # 计算下一帧时间（固定间隔，避免累积误差）
                next_frame_time = current_time + frame_interval
                        
        except Exception as e:
            logging.error(f"Streaming error: {e}")
        finally:
            try:
                if hasattr(self, 'pipe') and self.pipe:
                    self.pipe.stdin.close()
                    self.pipe.wait()
            except Exception as e:
                logging.error(f"Failed to close pipe: {e}")
                
    
                

    def saveImg(self):
        try:
            while self.is_switchOn or not self.save_queue.empty():
                if not self.save_queue.empty():
                    frame = self.save_queue.get()
                    try:
                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                    except Exception as e:
                        logging.error(f"Failed to write frame to save video: {e}")
                        break
                else:
                    time.sleep(0.01)  # 避免空队列时 CPU 占用过高
    
            # 视频保存完成后，上传到 MinIO
            if os.path.exists(self.save_full_video_path):
                return_video_name = minio_update.minio_interface(
                    self.dict_json, 
                    "full", 
                    os.path.basename(self.save_full_video_path), 
                    self.save_full_video_path
                )
                logging.info(f"Full video uploaded to MinIO: {return_video_name}")
            else:
                logging.error(f"Full video file not found: {self.save_full_video_path}")
    
        except Exception as e:
            logging.error(f"Video save error: {e}")
        finally:
            self.ffmpegSaveVideo.stdin.close()
            self.ffmpegSaveVideo.wait()
            logging.info("Video save process completed.")

        
    def set_switch_on(self, listenVule):
        self.is_switchOn = listenVule
        
    def set_video_name(self, video_name):
        self.video_name = video_name

    def set_mission_id(self, mission_id):
        self.mission_id = mission_id

    def _listen_for_stop(self):
        while self.is_switchOn:
            user_input = input("输入 'stop' 关闭视频流: ")
            if user_input.strip().lower() == 'stop':
                self.is_switchOn = False
                logging.info("收到关闭信号，正在关闭视频流...")
                break
            
    def listen_for_alert(self):
        while self.is_switchOn:
            if self.alert_flag:
                # 当 alert_flag 为 True 时执行特定操作
                self.handle_alert()
                self.alert_flag = False  # 重置 alert_flag
            time.sleep(0.1)  # 每 0.1 秒检查一次
    def handle_alert(self):
        # 在这里实现当 alert_flag 为 True 时需要执行的操作
        logging.info("Alert detected! Handling alert...")
        self.drone_state = drone_state_get(self.dict_json)
                
    
    # 原有的轨迹线绘制相关方法已被trajectory_interface.py替代

    def startThread1(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg)
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")
            
    def check_pipe_health(self):
        while self.is_switchOn:
            if self.pipe.poll() is not None:  # 检查 FFmpeg 进程是否已退出
                logging.error("FFmpeg process has exited unexpectedly.")
                self.is_switchOn = False
                break
            time.sleep(5)  # 每5秒检查一次
            
    def startThread(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg),
                threading.Thread(target=self.check_pipe_health)  # 新增健康检查线程
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except KeyboardInterrupt:
            logging.info("Received keyboard interrupt, stopping threads...")
            self.is_switchOn = False
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")

class StreamManager:
    def __init__(self, model_path):
        self.model_path = model_path
        self.streams = []
        self.lock = multiprocessing.Lock()
        self.processor = VideoStreamProcessor(model_path)
        self.active_processes = {}  # 用于存储活动的进程
        # self.active_streams = {}  # 用于记录正在运行的流地址

    def add_stream(self, dict_json, out_res):
        input_path = dict_json["droneStreamUrl"]
        
        output_url = dict_json["aiStreamUrl"]
        # input_path = "rtmp://************/stream"
        # output_url = "rtmp://***************:1936/live3/stream4"
        video_name = dict_json["taskId"]
        
        with self.lock:
            # 检查输入流是否已经启动
            # if input_path in self.active_streams:
            #     logging.warning(f"Stream {input_path} is already running.")
            #     return
            
            # 创建以 video_name 命名的文件夹
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            # 设置保存路径
            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            # 启动新流的处理进程
            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path)
            )
            process.start()
            
            # 存储进程和流地址
            self.active_processes[input_path] = process
            # self.active_streams[input_path] = True
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                # 设置流的开关为 False
                self.processor.stop_stream(input_url)
                process.join()  # 等待进程结束
                del self.active_processes[input_url]  # 从活动进程中移除
                del self.active_streams[input_url]  # 从活动流地址中移除
                logging.info(f"Stream {input_url} has been stopped.")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        with self.lock:
            return self.streams

    def is_stream_active(self, output_url):
        with self.lock:
            return output_url in self.active_streams


class VideoStreamProcessor:
    
    def __init__(self, model_path):
        self.model_path = model_path
        self.active_detectors = {}  # 用于存储活动的检测器
        self.lock = multiprocessing.Lock()  # 用于进程安全的操作

    def process_stream(self, dict_json, save_path):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        # input_path = "rtmp://************/stream"
        # output_url = "rtmp://***************:1936/live3/stream4"
        # video_name = dict_json["taskId"]
        
        
        detector = multiDealImg(self.model_path, save_path, dict_json)
        detector.set_switch_on(True)
        detector.set_video_name(video_name)
        
        with self.lock:
            self.active_detectors[input_path] = detector  # 存储检测器
        try:
            detector.startThread(input_path, output_url, save_path)
        finally:
            # 流处理结束后，清理资源
            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]  # 从活动检测器中移除
            logging.info(f"Stream {input_path} has finished and been removed from active streams.")
            

    
    def is_detector_active(self, input_path):
        with self.lock:
            return input_path in self.active_detectors
        
    def stop_stream(self, input_path):
        if input_path in self.active_detectors:
            detector = self.active_detectors[input_path]
            detector.set_switch_on(False)  # 设置流的开关为 False
            del self.active_detectors[input_path]  # 
            logging.info(f"Detector for stream {input_path} has been stopped.")
        else:
            logging.warning(f"Detector for stream {input_path} not found.")
                
    def process_streams(self, streams):
        processes = []
        for stream in streams:
            process = multiprocessing.Process(
                target=self.process_stream,
                args=(stream["input"], stream["output"], stream["save_path"], stream["video_name"])
            )
            process.start()
            processes.append(process)

        for process in processes:
            process.join()

    def process_streams_multiprocess(self, streams):
        processes = []
        for stream in streams:
            process = multiprocessing.Process(
                target=self.process_stream,
                args=(stream["input"], stream["output"], stream["save_path"], stream["video_name"])
            )
            process.start()
            processes.append(process)

        for process in processes:
            process.join()

# 定义比较函数，基于指定的关键字 
def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)

if __name__ == "__main__":

    
    setup_logging()
    #scheduler = BlockingScheduler()
    # model_path = "./model/best0701_640_2.pt"
    model_path = "./model/best_cj.pt"
    out_res = "./res"
    stream_manager = StreamManager(model_path)  
    
    # scheduler.add_job(drones_interface_run, 'interval', seconds=300) 
    #scheduler.start()
    # 主循环保持程序运行
    
    # 定义关键字列表
    keys = ["droneStreamUrl"]
    
    # active_drones_list = [
    #     {
    #         "droneDeviceSn": "1581F6Q8X24BJ00G011E",
    #         "area": "芜湖轨道机巢",
    #         "monitorEq": "芜湖轨道机巢无人机",
    #         "aiVendorInfo": {
    #             "id": "1",
    #             "aiAlgorithmTypes": "[]"
    #         },
    #         "droneFlightMode": "30",
    #         # "droneStreamUrl": "rtmp://***************:1935/live",
    #         "droneStreamUrl": "G:/工作/数据集/无人机视频/新建文件夹/1.mp4",
    #         "rootName": "中山北路",
    #         "gatewayDeviceSn": "7CTXMA600B02VF",
    #         "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
    #         "taskId": "test33",
    #         "aiStreamUrl":"result.mp4",
    #         "timestamp": "1750069019242"
    #     }
    # ]
    
    presentStream = []
    while True:
        # drones_interface_run()
        #获取token
        login_token_get()
        #获取正在飞行任务中的无人机列表
        active_drones_list = active_drones_get()
        # 获取 active_drones_list 中有且 presentStream 中没有的元素
        drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
        
        # print("active_streams = ", stream_manager.active_streams)
        if(len(drones_unique) > 0):
            for active_drone in drones_unique:        
              stream_manager.add_stream(active_drone, out_res)
        else:
            print(presentStream)
        presentStream = active_drones_list
        
        time.sleep(15)
    