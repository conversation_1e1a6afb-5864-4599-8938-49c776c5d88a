# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS
"""

#基于test10,将多线程改为多进程
import cv2
import os
import time
import queue
import threading
import multiprocessing
from collections import deque
import signal
import atexit
import psutil

from ultralytics import YOLO
import subprocess
import logging
import json

from nan import minio_update
from nan import drones_server
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

# 导入轨迹线接口
from trajectory_interface import TrajectoryRenderer


logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class multiDealImg(object):
    model = None  # 类变量，共享模型
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, model_path, save_path, dict_json):
        if multiDealImg.model is None:
            multiDealImg.model = YOLO(model_path).to('cuda')
        self.model = multiDealImg.model
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json  # 存储 initial_streams
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 添加资源管理相关属性
        self.threads = []  # 存储所有线程
        self.pipes = []    # 存储所有FFmpeg进程
        self.cleanup_done = False  # 防止重复清理

        # 注册清理函数
        atexit.register(self.cleanup_resources)

        self.save_queue = queue.Queue(maxsize=10)
        self.push_queue = queue.LifoQueue(maxsize=3)
        # 移除不必要的new_queue，直接使用frame_queue存储原始帧
        self.frame_queue = queue.Queue(maxsize=1)

        # self.names = ["HelAndRef", "Ref", "excavator", "NoHelAndRef", "Hel"]
        self.names = ["excavator", "crane", "compactor", "tank truck", "loader"]
        self.results = None
        self.dataT = 2
        

        # 告警相关
        self.buffer = deque(maxlen=125)  # 5秒缓存（25fps）
        self.alert_flag = False
        self.alert_frames = []
        self.alert_counter = 0
        self.alert_lock = threading.Lock()
        self.alert_info = {}  # 存储告警信息
        
        self.drone_state = {}
        
        self.drone_angle = 0
        # self.drone_angle = self.dict_json["attitude_head"]
        
        # 轨迹线动态参数设置（可手动调整）
        self.trajectory_length_multiplier = 2.0  # 轨迹线长度为视频高度的倍数
        self.trajectory_width_ratio = 0.5  # 两条轨迹线间距为视频宽度的比例
        
        # 手动轨迹线参数（设置为None时使用动态计算）
        self.manual_trajectory_length = None  # 手动设置的轨迹线长度（像素）
        self.manual_trajectory_width = None   # 手动设置的轨迹线宽度（像素）
        
        # 初始化轨迹线渲染器，启用动态尺寸调整
        self.trajectory_renderer = TrajectoryRenderer(
            enable_dynamic_sizing=True,
            length_multiplier=self.trajectory_length_multiplier,
            width_ratio=self.trajectory_width_ratio
        )
        
        # 设置手动参数（如果有的话）
        if self.manual_trajectory_length is not None or self.manual_trajectory_width is not None:
            self.trajectory_renderer.set_manual_params(
                length=self.manual_trajectory_length,
                width=self.manual_trajectory_width
            )
        
        # # 模拟航向角变化相关参数
        # self.simulated_heading = 0.0  # 当前模拟航向角
        # self.heading_direction = 1  # 航向角变化方向 (1为正向，-1为反向)
        # self.heading_change_rate = 5  # 航向角变化速率（度/帧）
        # self.heading_min = -180  # 最小航向角
        # self.heading_max = 180  # 最大航向角
        # self.frame_count = 0      # 帧计数器
        
    # def update_simulated_heading(self):
    #     """
    #     更新模拟航向角，实现在-90到90度之间的往复变化
    #     """
    #     # 更新航向角
    #     self.simulated_heading += self.heading_direction * self.heading_change_rate
        
        # 检查边界并改变方向
        # if self.simulated_heading >= self.heading_max:
        #     self.simulated_heading = self.heading_max
        #     self.heading_direction = -1  # 改为负方向
        # elif self.simulated_heading <= self.heading_min:
        #     self.simulated_heading = self.heading_min
        #     self.heading_direction = 1   # 改为正方向
        
        # return self.simulated_heading
        
        
    def open_ffmpeg_process(self, output_stream):
        # 优先尝试硬件编码，失败时自动回退到软件编码
        self.command = [
            'ffmpeg',
            '-y',
            '-re',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1280),
            '-r', '30',
            '-i', '-',
            '-c:v', 'libx264',  # 使用软件编码器libx264
            '-preset', 'fast',
            '-crf', '23',  # libx264支持crf参数
            '-g', '60',
            '-r', '30',
            '-pix_fmt', 'yuv420p',
            '-f', 'flv',
            output_stream
        ]
        try:
            self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
            logging.info("FFmpeg推流进程启动成功 (使用libx264)")
        except Exception as e:
            logging.error(f"FFmpeg推流进程启动失败: {e}")
            raise

    def openFfmpegSaveVideo(self, outputSaveVideo):
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1280", "-r", "30", "-i", "-",
            "-c:v", "libx264", "-pix_fmt", "yuv420p", "-preset", "fast",
            "-crf", "23", outputSaveVideo  # 使用软件编码器libx264
        ]
        try:
            self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
            logging.info("FFmpeg保存进程启动成功 (使用libx264)")
        except Exception as e:
            logging.error(f"FFmpeg保存进程启动失败: {e}")
            raise

    def read_video(self, video_path):
        cap = cv2.VideoCapture(video_path)
        logging.info(f"Attempting to open video stream: {video_path}")

        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
        else:
            logging.info(f"Successfully opened video stream: {video_path}")
    
        try:
            count = 0
            
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:  # 视频结束或读取失败
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                # 检查帧是否为空
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
    
                # 调整帧大小
                try:
                    frame = cv2.resize(frame, (1920, 1280))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 将原始帧放入队列供process_frame处理
                if not self.frame_queue.full():
                    self.frame_queue.put(frame)
                    count += 1
                    if count % 100 == 0:
                        logging.info(f"Frame {count} added to frame_queue")

        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def process_frame(self):
        try:
            count = 0
            alarmLevel = "2"
            alarmClass = "excavator"
            self.drone_angle = drone_yaw_get(self.dict_json)
            logging.info(f'process_frame中的航向角更新')
            print(self.drone_angle)
    
            while self.is_switchOn:
                t1 = time.time()
                frame = self.frame_queue.get()
                # frame = cv2.resize(frame, (640, 360))
                with self.model_lock:
                    self.results = self.model.track(frame, conf=0.45, show_conf=False, verbose=False, tracker='bytetrack.yaml')
                    # print(self.results)
                    if count % 50 == 0:
                        detection_count = len(self.results[0].boxes) if self.results and len(self.results) > 0 else 0
                        logging.info(f"Detection results obtained: {detection_count} objects detected")
                t2 = time.time()
    
                # 检测告警
                alert_triggered = False
                for result in self.results:
                    for box in result.boxes:
                        cls = int(box.cls)
                    
                        # 获取框坐标
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        
                        # 使用trajectory_interface计算轨迹线边界
                        frame_height, frame_width = frame.shape[:2]
                        trajectory_center_x = frame_width // 2  # 轨迹线中心X坐标
                        trajectory_start_y = frame_height  # 轨迹线起始Y坐标
                        
                        # 获取轨迹线边界
                        boundaries = self.trajectory_renderer.get_trajectory_boundaries(
                            center_x=trajectory_center_x,
                            start_y=trajectory_start_y,
                            heading_angle=self.drone_angle,
                            frame_width=frame_width,
                            frame_height=frame_height,
                            target_y=center_y
                        )
                        
                        # 判断物体位置
                        red_left = boundaries['red_left']
                        red_right = boundaries['red_right']
                        white_left = boundaries['white_left']
                        white_right = boundaries['white_right']
                        
                        if red_left <= center_x <= red_right:
                            # 位于两条红色轨迹线之间
                            alarmLevel = "1"
                        elif (white_left <= center_x < red_left) or (red_right < center_x <= white_right):
                            # 位于白色和红色轨迹线之间
                            alarmLevel = "2"
                        else:
                            # 位于其他区域
                            alarmLevel = "0"
                            
                        alarmClass = self.names[cls]
                        if alarmLevel == "1" or alarmLevel == "2":
                            alert_triggered = True
                            break
                    if alert_triggered:
                        break
    
                # 处理告警逻辑
                with self.alert_lock:
                    if alert_triggered:  # 即时触发告警保存
                        if not self.alert_flag:
                            # 保存前五秒的帧（当前buffer中的所有内容）
                            self.alert_frames = list(self.buffer)
                            self.alert_flag = True
                            self.alert_counter = 0
    
                    # 缓存当前帧
                    self.buffer.append(frame.copy())
    
                    if self.alert_flag:
                        self.alert_frames.append(frame.copy())
                        # self.alert_counter += 1
                        # if self.alert_counter >= 150:  # 保存前后各五秒的帧（总共约10秒）
                        self.save_alert_video(alarmLevel, alarmClass)
                        self.alert_flag = False
                        self.alert_frames = []
                        self.alert_counter = 0
                print('333行绘制检测框')
                # 绘制检测框
                self.save_queue.put(frame.copy())   #放在这里是保存原视频
                
                # 创建带检测框和轨迹线的帧用于显示
                display_frame = frame.copy()
                
                # 先绘制轨迹线
                display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)
                
                # 再绘制检测框 
                try:
                    if self.results:
                        # 使用第一个结果进行绘制，需要在display_frame上绘制
                        display_frame = self.results[0].plot(img=display_frame, conf=True, line_width=2, font_size=0.5)
                        logging.info(f'结果绘制成功')
                except Exception as e:
                    logging.error(f"Error plotting detection results: {e}")
                
                # 将带检测框和轨迹线的帧放入推流队列
                if not self.push_queue.full():
                    self.push_queue.put(display_frame)
                    logging.info(f'将带检测框和轨迹线的帧放入推流队列')
                
                # 保存带检测框和轨迹线的帧到文件夹
                if count % 30 == 0:  # 每30帧保存一次，避免保存过多图片
                    try:
                        # 创建保存目录
                        save_dir = os.path.join(self.result_path, "detection_frames")
                        if not os.path.exists(save_dir):
                            os.makedirs(save_dir)
                            logging.info(f"Created detection frames directory: {save_dir}")
                        
                        # 生成文件名
                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                        frame_filename = f"detection_frame_{timestamp}_{count}.jpg"
                        frame_path = os.path.join(save_dir, frame_filename)
                        
                        # 保存图片
                        cv2.imwrite(frame_path, display_frame)
                        
                        logging.info(f"Detection frame saved: {frame_path}")
                    except Exception as e:
                        logging.error(f"Failed to save detection frame: {e}")
                
                
                self.dataT = time.time() - t1
    
                if count % 100 == 99:
                    logging.info(f"Inference time: {self.dataT:.4f}s")
                    count = 0
                count += 1
        except Exception as e:
            logging.error(f"Frame processing error: {e}")

        
    def save_alert_video(self, alarm_level, alarmClass):
        if not self.alert_frames:
            return
        
        # 创建以 video_name 命名的文件夹
        alert_video_dir = os.path.join(self.result_path, self.video_name)
        
        if not os.path.exists(alert_video_dir):
            os.makedirs(alert_video_dir)
            logging.info(f"Created folder: {alert_video_dir}")
        
        # 生成时间戳
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # 告警视频路径
        alert_video_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.mp4"
        )
        
        # 告警图片路径
        alert_image_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.jpg"
        )
        
        # 保存告警图片
        try:
            cv2.imwrite(alert_image_path, self.alert_frames[-1])  # 保存告警图片
            logging.info(f"Alert image saved successfully: {alert_image_path}")
        except Exception as e:
            logging.error(f"Failed to save alert image: {e}")
        
        # 保存告警视频
        threading.Thread(
            target=self.write_alert_video, 
            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)
        ).start()
        
        # 存储告警信息
        self.alert_info = {
            "alert_image_path": alert_image_path,
            "alert_video_path": alert_video_path,
            "full_video_path": os.path.join(self.result_path, f"{self.video_name}.mp4"),
            "stream_url": self.dict_json["droneStreamUrl"]
        }
        logging.info(f"Alert info: {json.dumps(self.alert_info)}")
        
        # 调用 minio_update 和 alarm_info_post
        # self.call_minio_and_alarm(alert_image_path, alert_video_path)
        
    
    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):
        # 调用 minio_update
        logging.info(f"Uploading image to MinIO: {alert_image_path}")
        return_image_name = minio_update.minio_interface(
            self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
        )
        logging.info(f"Image uploaded to MinIO: {return_image_name}")
        
        logging.info(f"Uploading video to MinIO: {alert_video_path}")
        return_video_name = minio_update.minio_interface(
            self.dict_json, "clip", os.path.basename(alert_video_path), alert_video_path
        )
        logging.info(f"Video uploaded to MinIO: {return_video_name}")
        
        # 调用 alarm_info_post
        logging.info(f"Posting alarm info to server")
        
        self.drone_state = drone_state_get(self.dict_json)

        drones_server.alarm_info_post(
            active_drone=self.dict_json,
            drone_state=self.drone_state,
            classes=alarmClass,  # 告警类别
            alarmLevel=alarm_level,  # 告警级别
            alarmImageUrl=return_image_name,  # 告警图片路径
            videoUrl=return_video_name  # 告警视频路径
        )
        logging.info(f"Alarm info posted to server")

    # @staticmethod
    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):
        command = [
            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',
            '-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-preset', 'fast',
            '-crf', '23', alert_video_path  # 使用软件编码器libx264
        ]
        try:
            pipe = subprocess.Popen(command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
            for frame in frames:
                pipe.stdin.write(frame.tobytes())
            pipe.stdin.close()
            pipe.wait()
            logging.info(f"Alert video saved: {alert_video_path}")
            
            # 异步调用 minio_update.minio_interface
            threading.Thread(
                target=self.call_minio_and_alarm, 
                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)
            ).start()
        except Exception as e:
            logging.error(f"Failed to save alert video: {e}")   

                
                
    def pushImg(self):
        try:
            frame_count = 0  # 帧计数器
            frame_interval = 1 / 30  # 目标帧率 30fps，每帧间隔 0.033 秒
            last_frame_time = time.time()  # 记录上一帧的时间
            logging.info(f'推流线程启动')
            while self.is_switchOn:
                t_start = time.time()

                # 优先从push_queue获取已处理的帧（带检测框和轨迹线）
                if not self.push_queue.empty():
                    frame = self.push_queue.get()
                    logging.info(f'从push_queue获取帧进行推流')
                else:
                    time.sleep(0.001)  # 避免空队列时 CPU 占用过高
                    continue
                
                # 推流
                try:
                    # 检查管道是否仍然有效
                    if self.pipe.poll() is not None:
                        logging.error("FFmpeg process has exited, stopping push")
                        break
                    
                    self.pipe.stdin.write(frame.tobytes())
                    if frame_count % 100 == 0:
                        logging.info(f"Frame {frame_count} successfully pushed to stream")
                except BrokenPipeError:
                    logging.error("Broken pipe detected, FFmpeg process may have crashed")
                    break
                except Exception as e:
                    logging.error(f"Failed to write frame to pipe: {e}")
                    break
                
                # 计算处理时间
                elapsed = time.time() - t_start
                frame_count += 1
                
                # 每100帧打印一次平均处理时间
                if frame_count % 100 == 0:
                    logging.info(f"push time =: {elapsed}")
                
                # 精确帧率控制
                next_frame_time = last_frame_time + frame_interval
                sleep_time = max(0.0, next_frame_time - time.time())
                time.sleep(sleep_time)
                
                final_push_time = time.time() -  t_start                 
                if frame_count % 100 == 0:
                    logging.info(f"final time =: {final_push_time}")
                
        except Exception as e:
            logging.error(f"Streaming error: {e}")
        finally:
            try:
                self.pipe.stdin.close()
                self.pipe.wait()
            except Exception as e:
                logging.error(f"Failed to close pipe: {e}")

                

    def saveImg(self):
        try:
            while self.is_switchOn or not self.save_queue.empty():
                if not self.save_queue.empty():
                    frame = self.save_queue.get()
                    try:
                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                    except Exception as e:
                        logging.error(f"Failed to write frame to save video: {e}")
                        break
                else:
                    time.sleep(0.01)  # 避免空队列时 CPU 占用过高
    
            # 视频保存完成后，上传到 MinIO
            if os.path.exists(self.save_full_video_path):
                return_video_name = minio_update.minio_interface(
                    self.dict_json, 
                    "full", 
                    os.path.basename(self.save_full_video_path), 
                    self.save_full_video_path
                )
                logging.info(f"Full video uploaded to MinIO: {return_video_name}")
            else:
                logging.error(f"Full video file not found: {self.save_full_video_path}")
    
        except Exception as e:
            logging.error(f"Video save error: {e}")
        finally:
            self.ffmpegSaveVideo.stdin.close()
            self.ffmpegSaveVideo.wait()
            logging.info("Video save process completed.")

        
    def set_switch_on(self, listenVule):
        self.is_switchOn = listenVule
        
    def set_video_name(self, video_name):
        self.video_name = video_name

    def set_mission_id(self, mission_id):
        self.mission_id = mission_id

    def _listen_for_stop(self):
        while self.is_switchOn:
            user_input = input("输入 'stop' 关闭视频流: ")
            if user_input.strip().lower() == 'stop':
                self.is_switchOn = False
                logging.info("收到关闭信号，正在关闭视频流...")
                break
            
    def listen_for_alert(self):
        while self.is_switchOn:
            if self.alert_flag:
                # 当 alert_flag 为 True 时执行特定操作
                self.handle_alert()
                self.alert_flag = False  # 重置 alert_flag
            time.sleep(0.1)  # 每 0.1 秒检查一次
    def handle_alert(self):
        # 在这里实现当 alert_flag 为 True 时需要执行的操作
        logging.info("Alert detected! Handling alert...")
        self.drone_state = drone_state_get(self.dict_json)
                
    
    # 原有的轨迹线绘制相关方法已被trajectory_interface.py替代

    def startThread1(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg)
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")
            
    def check_pipe_health(self):
        while self.is_switchOn:
            if self.pipe.poll() is not None:  # 检查 FFmpeg 进程是否已退出
                logging.error("FFmpeg process has exited unexpectedly.")
                self.is_switchOn = False
                break
            time.sleep(5)  # 每5秒检查一次
            
    def startThread(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)

            # 创建线程并添加到管理列表
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,), name="read_video"),
                threading.Thread(target=self.process_frame, name="process_frame"),
                threading.Thread(target=self.pushImg, name="pushImg"),
                threading.Thread(target=self.saveImg, name="saveImg"),
                threading.Thread(target=self.check_pipe_health, name="check_pipe_health")
            ]

            # 设置线程为daemon并启动
            for t in threads:
                t.daemon = True
                self.threads.append(t)
                t.start()

            # 等待所有线程完成
            for t in threads:
                t.join()

        except KeyboardInterrupt:
            logging.info("收到键盘中断信号，正在停止线程...")
            self.is_switchOn = False
        except Exception as e:
            logging.error(f"线程启动失败: {e}")
        finally:
            # 确保资源被清理
            self.cleanup_resources()

    def cleanup_resources(self):
        """清理所有资源"""
        if self.cleanup_done:
            return

        logging.info("开始清理资源...")

        # 1. 设置停止标志
        self.is_switchOn = False

        # 2. 清理FFmpeg进程
        try:
            if hasattr(self, 'pipe') and self.pipe:
                if self.pipe.poll() is None:  # 进程仍在运行
                    self.pipe.stdin.close()
                    self.pipe.terminate()
                    self.pipe.wait(timeout=5)
                logging.info("FFmpeg推流进程已关闭")
        except Exception as e:
            logging.error(f"关闭FFmpeg推流进程时出错: {e}")
            try:
                if hasattr(self, 'pipe') and self.pipe:
                    self.pipe.kill()
            except:
                pass

        try:
            if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:
                if self.ffmpegSaveVideo.poll() is None:
                    self.ffmpegSaveVideo.stdin.close()
                    self.ffmpegSaveVideo.terminate()
                    self.ffmpegSaveVideo.wait(timeout=5)
                logging.info("FFmpeg保存进程已关闭")
        except Exception as e:
            logging.error(f"关闭FFmpeg保存进程时出错: {e}")
            try:
                if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:
                    self.ffmpegSaveVideo.kill()
            except:
                pass

        # 3. 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                try:
                    thread.join(timeout=2.0)
                    if thread.is_alive():
                        logging.warning(f"线程 {thread.name} 超时未结束")
                except Exception as e:
                    logging.error(f"等待线程结束时出错: {e}")

        # 4. 清空所有队列
        try:
            while not self.frame_queue.empty():
                self.frame_queue.get_nowait()
            while not self.push_queue.empty():
                self.push_queue.get_nowait()
            while not self.save_queue.empty():
                self.save_queue.get_nowait()
            logging.info("所有队列已清空")
        except Exception as e:
            logging.error(f"清空队列时出错: {e}")

        self.cleanup_done = True
        logging.info("资源清理完成")

class StreamManager:
    def __init__(self, model_path):
        self.model_path = model_path
        self.streams = []
        self.lock = multiprocessing.Lock()
        self.processor = VideoStreamProcessor(model_path)
        self.active_processes = {}
        self.cleanup_done = False

        # 注册信号处理器和清理函数
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        atexit.register(self.cleanup_all_resources)

    def signal_handler(self, signum, frame):
        """处理系统信号"""
        logging.info(f"收到信号 {signum}，开始清理所有资源...")
        self.cleanup_all_resources()

    def cleanup_all_resources(self):
        """清理所有流管理器的资源"""
        if self.cleanup_done:
            return

        logging.info("开始清理StreamManager资源...")

        with self.lock:
            # 停止所有活动进程
            for input_path, process in list(self.active_processes.items()):
                try:
                    if process.is_alive():
                        logging.info(f"正在停止进程: {input_path}")
                        self.processor.stop_stream(input_path)
                        process.terminate()
                        process.join(timeout=5)
                        if process.is_alive():
                            logging.warning(f"进程 {input_path} 超时未结束，强制杀死")
                            process.kill()
                            process.join()
                        logging.info(f"进程 {input_path} 已停止")
                except Exception as e:
                    logging.error(f"停止进程 {input_path} 时出错: {e}")

            self.active_processes.clear()
            self.streams.clear()

        self.cleanup_done = True
        logging.info("StreamManager资源清理完成")

    def add_stream(self, dict_json, out_res):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        with self.lock:
            # 创建保存文件夹
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            # 启动新流的处理进程
            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path),
                name=f"stream_{video_name}"
            )
            process.start()
            logging.info(f'推流进程已开启: {video_name}')

            # 存储进程信息
            self.active_processes[input_path] = process
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                try:
                    # 设置流的开关为 False
                    self.processor.stop_stream(input_url)

                    # 等待进程正常结束
                    process.join(timeout=10)

                    # 如果进程仍在运行，强制终止
                    if process.is_alive():
                        logging.warning(f"进程 {input_url} 超时未结束，强制终止")
                        process.terminate()
                        process.join(timeout=5)

                        if process.is_alive():
                            process.kill()
                            process.join()

                    del self.active_processes[input_url]

                    # 从流列表中移除
                    self.streams = [s for s in self.streams if s["input"] != input_url]

                    logging.info(f"Stream {input_url} has been stopped.")
                except Exception as e:
                    logging.error(f"停止流 {input_url} 时出错: {e}")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        with self.lock:
            return self.streams

    def is_stream_active(self, input_url):
        with self.lock:
            return input_url in self.active_processes and self.active_processes[input_url].is_alive()

class VideoStreamProcessor:

    def __init__(self, model_path):
        self.model_path = model_path
        self.active_detectors = {}  # 用于存储活动的检测器
        self.lock = multiprocessing.Lock()  # 用于进程安全的操作

    def process_stream(self, dict_json, save_path):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]

        detector = None
        try:
            detector = multiDealImg(self.model_path, save_path, dict_json)
            detector.set_switch_on(True)
            detector.set_video_name(video_name)

            with self.lock:
                self.active_detectors[input_path] = detector

            detector.startThread(input_path, output_url, save_path)

        except Exception as e:
            logging.error(f"处理流 {input_path} 时出错: {e}")
        finally:
            # 确保资源被清理
            if detector:
                detector.cleanup_resources()

            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]
            logging.info(f"Stream {input_path} 处理完成并清理资源")

    def stop_stream(self, input_path):
        with self.lock:
            if input_path in self.active_detectors:
                detector = self.active_detectors[input_path]
                detector.set_switch_on(False)
                detector.cleanup_resources()
                logging.info(f"Detector for stream {input_path} has been stopped.")
            else:
                logging.warning(f"Detector for stream {input_path} not found.")

    def is_detector_active(self, input_path):
        with self.lock:
            return input_path in self.active_detectors

# 定义比较函数，基于指定的关键字
def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)

# 添加全局清理函数
def setup_global_cleanup():
    """设置全局资源清理"""
    def cleanup_handler(signum, frame):
        logging.info("收到退出信号，正在清理全局资源...")
        # 强制清理所有资源并退出
        try:
            # 获取当前进程的所有子进程
            current_process = psutil.Process()
            for child in current_process.children(recursive=True):
                try:
                    child.terminate()
                    child.wait(timeout=3)
                    if child.is_running():
                        child.kill()
                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    pass
        except Exception as e:
            logging.error(f"清理子进程时出错: {e}")

        logging.info("全局资源清理完成")
        os._exit(0)

    signal.signal(signal.SIGINT, cleanup_handler)
    signal.signal(signal.SIGTERM, cleanup_handler)

if __name__ == "__main__":
    # 设置全局清理
    setup_global_cleanup()

    setup_logging()

    # model_path = "./model/best0701_640_2.pt"
    model_path = "/root/ultralytics-main/model/best_cj.pt"
    out_res = "./res"
    stream_manager = StreamManager(model_path)

    # 定义关键字列表
    keys = ["droneStreamUrl"]

    presentStream = []

    try:
        while True:
            # 获取token
            login_token_get()
            # 获取正在飞行任务中的无人机列表
            active_drones_list = active_drones_get()
            # 获取 active_drones_list 中有且 presentStream 中没有的元素
            drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
            print(drones_unique)

            if(len(drones_unique) > 0):
                for active_drone in drones_unique:
                    print("现在执行active_drone的for循环")
                    stream_manager.add_stream(active_drone, out_res)
            else:
                print(presentStream)
                # print('111111111111111未检测')

            presentStream = active_drones_list
            time.sleep(15)

    except KeyboardInterrupt:
        logging.info("收到键盘中断，正在关闭程序...")
    except Exception as e:
        logging.error(f"主程序运行出错: {e}")
    finally:
        # 确保清理所有资源
        stream_manager.cleanup_all_resources()
        logging.info("程序正常退出")
