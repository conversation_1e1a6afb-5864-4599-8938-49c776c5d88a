import os
from datetime import timedelta
import hashlib
import time
from datetime import datetime
import traceback
from minio import Minio, S3Error
from mimetypes import guess_type
import nan.constants

import logging

# 获取本模块的日志记录器
logger = logging.getLogger(__name__)

# 初始化MinIO客户端
client = Minio(
    endpoint=nan.constants.minioInfo.endpoint,
    access_key=nan.constants.minioInfo.access_key,
    secret_key=nan.constants.minioInfo.secret_key,
    secure=nan.constants.minioInfo.secure
)

def object_name_get(active_drone,file_type,file_name):

    """获取minio文件地址
    Args:
        active_drone   (List):获取正在飞行任务中的无人机列表接口返回的本次任务的相关信息
        file_type      (String):文件类型  full  # 全线巡检视频  alarm/ # 报警图片  clip  # 报警前后视频片段
        file_name      (String):文件名称(不带文件路径)
    Returns:
        object_name    (String):minio中存储路径
    """
    object_name = ""
    try:
        task_id = active_drone.get("taskId")
        drone_id = active_drone.get("droneDeviceSn")
        current_date = datetime.now()
        formatted_date = current_date.strftime("%Y%m%d")
        base_path = f"{formatted_date}/task-{task_id}/vendor-{nan.constants.aiVendorName}/drone-{drone_id}/{file_type}"
        object_name = f"{base_path}/{file_name}"  # 完整对象名
        logger.info(f"object_name_get######minio中存储路径：{object_name}")
        
    except Exception as e:
        logger.error(f"object_name_get######获取minio文件地址错误: {e}")
        traceback.print_exc()  
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
        
    return object_name


def upload_to_minio(object_name, file_path):
    # 动态生成对象路径（关键步骤）
    try:
        # 检查存储桶是否存在（可选）
        if not client.bucket_exists(nan.constants.minioInfo.bucket_name):
            client.make_bucket(nan.constants.minioInfo.bucket_name)
        # 上传文件（自动创建路径结构）
        client.fput_object(
            bucket_name=nan.constants.minioInfo.bucket_name,
            object_name=object_name,
            file_path=file_path
        )
        logger.info(f"upload_to_minio######文件上传至minio：{object_name}")
        print(f"upload_to_minio######文件上传至minio：{object_name}")
        return object_name
    except S3Error as e:
        logger.error(f"upload_to_minio######minio上传错误: {e}")
        print(f"upload_to_minio######minio上传错误: {e}")
        return ""
    except Exception as e:
        logger.error(f"upload_to_minio######内部错误错误: {e}")
        print(f"upload_to_minio######内部错误错误: {e}")
        traceback.print_exc()  
        error_msg = traceback.format_exc() 
        logger.error(f"{error_msg}")
        return ""
    

def minio_interface(active_drone,file_type,file_name, file_path):
    
    object_name = object_name_get(active_drone,file_type,file_name)
    return_name = upload_to_minio(object_name, file_path)
    return return_name

# 调用示例
if __name__ == "__main__":

    active_drone = {"droneDeviceSn": "1581F6Q8X24BM00G016L","aiVendorInfo": "","droneFlightMode": "","droneStreamUrl": "rtmp://36.138.184.233:1935/live10/stream10","rootName": "轨道巡检","gatewayDeviceSn": "7CTXMBL00B04HF","uuid": "3379b7c3-dff4-4b24-adda-375aaceca50b","taskId": "1d1185a9-86ca-4c76-a3b6-cdedfcffc598","aiStreamUrl": "","timestamp": "1749780914135"}
    file_type = "alarm"
    file_name = "1.mp4"
    file_path = "1.mp4"
    return_object_name = minio_interface(active_drone,file_type,file_name, file_path)
    
    print(f"📁 文件已存储至: minio://{return_object_name}")
        
        

