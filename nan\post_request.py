import json
import requests

import logging

# 获取本模块的日志记录器
logger = logging.getLogger(__name__)



def post_requests_response(url,data,headers,params):
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers,params=params)
        
        # 检查HTTP状态码
        if response.status_code != 200:
            logger.error(f"HTTP请求失败，状态码：{response.status_code}")
            return {}
        
        # 解析JSON响应
        json_data = response.json()
        
        # 检查code字段
        if json_data.get("code") == 200:
            # 返回token值
            return json_data
        else:
            logger.error(f"业务逻辑错误：{json_data}")
            return {}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求异常：{str(e)}")
    except ValueError:
        logger.error("响应内容不是有效的JSON格式")
    return {}


# 使用示例
if __name__ == "__main__":
    import nan.constants
    data = {
    "deviceType": 1,
    "aiAlgorithmVendorId": 0,
    "alarmId": "70130f2a-0727-4cbd-916c-1e3338bf4d1e",
    "taskId": "1d1185a9-86ca-4c76-a3b6-cdedfcffc598",
    "gatewayDeviceSn": "7CTXMA600B02VF",
    "droneDeviceSn": "1581F6Q8X24BJ00G011E",
    "classes": "NoHelAndRef",
    "taskType": 1,
    "alarmLevel": 1,
    "monitorEq": "芜湖轨道机巢无人机",
    "area": "芜湖轨道机巢",
    "alarmLine": "短航线测试",
    "alarmTimestamp": 1750231668204,
    "alarmImageUrl": "20250617/task-test1/vendor-test/drone-1581F6Q8X24BJ00G011E/alarm/alert_test1_20250617-170031.jpg",
    "videoUrl": "20250617/task-test1/vendor-test/drone-1581F6Q8X24BJ00G011E/clip/alert_test1_20250617-170031.mp4",
    "alarmAccuratePosition": "118.36614019021567,31.334333187994428"}
    token = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA0NDUzMDEsInVzZXJuYW1lIjoiYWRtaW4ifQ.2qv2QsZO2EDz0OhtVY2jJYlalY4ZYjIT0c4WP7jr3WA"
    url = nan.constants.baseURL + nan.constants.alarmAddr
    headers = {"Content-Type": "application/json","Authorization":token}
    params = {}
    token = post_requests_response(url,data,headers,params)
    print(f"获取到的返回：{token}")