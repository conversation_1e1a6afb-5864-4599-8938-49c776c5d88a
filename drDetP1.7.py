# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS
"""

#基于test10,将多线程改为多进程
import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque

from ultralytics import YOLO
import subprocess
from utils.plots import Annotator, colors, save_one_box
import logging
import json

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math

import sys
from apscheduler.schedulers.blocking import BlockingScheduler


logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class multiDealImg(object):
    model = None  # 类变量，共享模型
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, model_path, save_path, dict_json):
        if multiDealImg.model is None:
            multiDealImg.model = YOLO(model_path).to('cuda')
        self.model = multiDealImg.model
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json  # 存储 initial_streams
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        self.save_queue = queue.Queue(maxsize=10)
        self.push_queue = queue.LifoQueue(maxsize=3)
        self.new_queue = queue.LifoQueue(maxsize=1)
        self.frame_queue = queue.Queue(maxsize=1)

        # self.names = ["HelAndRef", "Ref", "excavator", "NoHelAndRef", "Hel"]
        self.names = ["excavator", "crane", "compactor", "tank truck", "loader"]
        self.results = None
        self.dataT = 2
        
        #图中画两根虚线左右宽度像素点
        self.left_line = 160
        self.right_line = 1128

        # 告警相关
        self.buffer = deque(maxlen=125)  # 5秒缓存（25fps）
        self.alert_flag = False
        self.alert_frames = []
        self.alert_counter = 0
        self.alert_lock = threading.Lock()
        self.alert_info = {}  # 存储告警信息
        
        # self.alert_listener_thread = threading.Thread(target=self.listen_for_alert)
        # self.alert_listener_thread.daemon = True
        # self.alert_listener_thread.start()
        
        self.drone_state = {}
        
        self.drone_angle = 0
        # self.radius = 6000
        # self.base_points = (260, 470, 810, 1020)
        self.base_points = (170, 750, 1100, 1600)
        
        
        
    def open_ffmpeg_process(self, output_stream):
        self.command = [
            'ffmpeg',
            '-y',
            '-re',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1280),
            # '-r', '30',
            '-i', '-',
            '-c:v', 'h264_nvenc',
            '-preset', 'fast',
            '-g', '60',
            # '-tune', 'zerolatency',  # 低延迟模式
            '-b:v', '1M',  # 降低码率
            '-maxrate', '1M',  # 限制最大码率
            '-bufsize', '2M',  # 减少缓冲区大小
            # '-r', '30',
            '-pix_fmt', 'yuv420p',
            '-crf', '23',
            '-f', 'flv',
            output_stream
        ]
    
        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE)

    def openFfmpegSaveVideo(self, outputSaveVideo):
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1280", "-r", "30", "-i", "-",
            "-c:v", "h264_nvenc", "-pix_fmt", "yuv420p", "-crf", "23", "-preset", "fast", outputSaveVideo
        ]
        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE)

    def read_video(self, video_path):
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
    
        try:
            # height = 720
            # width = 1280
            
            # x1 = width // 8
            # x2 = 7 * width // 8
            pre_angle = 0
            
            count = 0
            angle = 1
            
            line_color = (0, 0, 255)  # 红色 (BGR格式)
            line_thickness = 2  # 线宽
            line_type = cv2.LINE_AA  # 抗锯齿线
            dash_length = 10  # 虚线每段的长度
            gap_length = 5  # 虚线每段之间的间隔
            tilt_angle1 = -4
            tilt_angle2 = 4
            while self.is_switchOn:
                
                start_time = time.time()
                
                ret, frame = cap.read()
                if not ret:  # 视频结束或读取失败
                    # cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    # continue
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                # 检查帧是否为空
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
    
                # 调整帧大小
                try:
                    frame = cv2.resize(frame, (1920, 1280))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 将帧放入队列
                if not self.frame_queue.full():
                    self.frame_queue.put(frame)
                if not self.new_queue.full():
                    # current_angle = self.drone_angle  # 使用属性获取器自动加锁
                    # if pre_angle != current_angle:
                    #     frame = self.draw_all_Curve(frame, current_angle, self.base_points)
                    #     pre_angle = current_angle
                    # if count == 1:
                        # logging.info("#############################111")
                    self.drone_angle = drone_yaw_get(self.dict_json)
                    # self.drone_angle = 0
                        # logging.info("#############################222")
                        # self.drone_angle = 1
                        
                        # logging.info("#############################")
                        # 计算圆心和半径
                    if self.drone_angle < 0.1 and self.drone_angle > -0.1:
                        
                        for y in range(0, 1280, dash_length + gap_length): 
                            start_point = (int(self.base_points[0] + y * math.tan(math.radians(tilt_angle1))), y)                            
                            end_point = (int(self.base_points[0] + (y + dash_length) * math.tan(math.radians(tilt_angle1))), y + dash_length)
                            
                            # cv2.line(frame, (self.base_points[0], y), (self.base_points[0], y + dash_length), (0, 255, 255), 2, line_type) 
                            cv2.line(frame, start_point, end_point, (0, 255, 255), line_thickness, line_type)
                        
                        for y in range(0, 1280, dash_length + gap_length):
                            # cv2.line(frame, (self.base_points[1], y), (self.base_points[1], y + dash_length), (0, 0, 255), 2, line_type)
                            start_point = (int(self.base_points[1] + y * math.tan(math.radians(tilt_angle1))), y)                            
                            end_point = (int(self.base_points[1] + (y + dash_length) * math.tan(math.radians(tilt_angle1))), y + dash_length)
 
                            cv2.line(frame, start_point, end_point, (0, 0, 255), line_thickness, line_type)
                            
                        for y in range(0, 1280, dash_length + gap_length):
                            # cv2.line(frame, (self.base_points[2], y), (self.base_points[2], y + dash_length), (0, 0, 255), 2, line_type)
                            start_point = (int(self.base_points[2] + y * math.tan(math.radians(tilt_angle2))), y)                            
                            end_point = (int(self.base_points[2] + (y + dash_length) * math.tan(math.radians(tilt_angle2))), y + dash_length)
 
                            cv2.line(frame, start_point, end_point, (0, 0, 255), line_thickness, line_type)
                            
                            
                        for y in range(0, 1280, dash_length + gap_length):
                            # cv2.line(frame, (self.base_points[3], y), (self.base_points[3], y + dash_length), (0, 255, 255), 2, line_type)
                            start_point = (int(self.base_points[3] + y * math.tan(math.radians(tilt_angle2))), y)                            
                            end_point = (int(self.base_points[3] + (y + dash_length) * math.tan(math.radians(tilt_angle2))), y + dash_length)
 
                            cv2.line(frame, start_point, end_point, (0, 255, 255), line_thickness, line_type)
                    else:
                        center1, center2, center3, center4, radius = self.calculate_centers(self.drone_angle, self.base_points, frame.shape[0])                        
                        # 绘制圆弧（四条曲线）
                        # result = frame.copy()
                        # 第一组圆心：两条曲线
                        frame = self.draw_smooth_curve(frame, center1, radius, (0, 255, 255))         # 红色
                        frame = self.draw_smooth_curve(frame, center2, radius, (0, 0, 255))      # 黄色
                        # 第二组圆心：两条曲线
                        frame = self.draw_smooth_curve(frame, center3, radius, (0, 0, 255))      # 黄色
                        frame = self.draw_smooth_curve(frame, center4, radius, (0, 255, 255))        # 红色
                        # frame = self.draw_all_Curve(frame, count%85, self.base_points)
                    # count += 1
                    # if count > 2:
                    #     count = 0
                        
                    self.new_queue.put(frame)
                
        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def process_frame(self):
        try:
            count = 0
            consecutive_alerts = 0  # 新增：用于计数连续触发的告警次数
            alarmLevel = "2"
            alarmClass = "excavator"
    
            while self.is_switchOn:
                t1 = time.time()
                frame = self.frame_queue.get()
                # frame = cv2.resize(frame, (640, 360))
                with self.model_lock:
                    self.results = self.model(frame, conf=0.45, show_conf=False, verbose=False)
                t2 = time.time()
    
                # 检测告警
                alert_triggered = False
                for result in self.results:
                    for box in result.boxes:
                        cls = int(box.cls)
                    
                        # 获取框坐标
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        
                        if self.drone_angle > -1 and self.drone_angle < 1:
                            if self.base_points[1] < center_x < self.base_points[2]:  # 在虚线之间
                                alarmLevel = "1"
                            elif self.base_points[0] < center_x < self.base_points[1] or self.base_points[2] < center_x < self.base_points[3]:                                
                                alarmLevel = "2"  
                            else:
                                alarmLevel = "0"
                        else:
                            center1, center2, center3, center4, radius = self.calculate_centers(self.drone_angle, self.base_points, frame.shape[0])
                                
                            alarmLevel = self.is_point_between_curves((center_x, center_y), (center1, center2, center3, center4), radius)
                        alarmClass = self.names[cls]
                        if alarmLevel == "1" or alarmLevel == "2":
                            alert_triggered = True
                            break
                    if alert_triggered:
                        break
    
                # 更新连续触发的告警次数
                if alert_triggered:
                    consecutive_alerts += 1
                else:
                    consecutive_alerts = 0
    
                # 处理告警逻辑
                with self.alert_lock:
                    if consecutive_alerts >= 5:  # 新增：只有当连续3帧检测到告警时才进行保存
                        if not self.alert_flag:
                            # 保存前五秒的帧（当前buffer中的所有内容）
                            self.alert_frames = list(self.buffer)
                            self.alert_flag = True
                            self.alert_counter = 0
    
                    # 缓存当前帧
                    self.buffer.append(frame.copy())
    
                    if self.alert_flag:
                        self.alert_frames.append(frame.copy())
                        self.alert_counter += 1
                        if self.alert_counter >= 250:  # 保存前后各五秒的帧（总共约10秒）
                            self.save_alert_video(alarmLevel, alarmClass)
                            self.alert_flag = False
                            self.alert_frames = []
                            self.alert_counter = 0
    
                # 绘制检测框
                self.save_queue.put(frame)   #放在这里是保存原视频
                for result in self.results:
                    frame = result.plot(conf=False, line_width=1, font_size=0.2)
                # self.save_queue.put(frame)
                self.dataT = time.time() - t1
    
                if count % 100 == 99:
                    logging.info(f"Inference time: {self.dataT:.4f}s")
                    count = 0
                count += 1
        except Exception as e:
            logging.error(f"Frame processing error: {e}")

        
    def save_alert_video(self, alarm_level, alarmClass):
        if not self.alert_frames:
            return
        
        # 创建以 video_name 命名的文件夹
        alert_video_dir = os.path.join(self.result_path, self.video_name)
        
        if not os.path.exists(alert_video_dir):
            os.makedirs(alert_video_dir)
            logging.info(f"Created folder: {alert_video_dir}")
        
        # 生成时间戳
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # 告警视频路径
        alert_video_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.mp4"
        )
        
        # 告警图片路径
        alert_image_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.jpg"
        )
        
        # 保存告警图片
        cv2.imwrite(alert_image_path, self.alert_frames[-1])  # 保存告警图片
        
        # 保存告警视频
        threading.Thread(
            target=self.write_alert_video, 
            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)
        ).start()
        
        # 存储告警信息
        self.alert_info = {
            "alert_image_path": alert_image_path,
            "alert_video_path": alert_video_path,
            "full_video_path": os.path.join(self.result_path, f"{self.video_name}.mp4"),
            "stream_url": self.dict_json["droneStreamUrl"]
        }
        logging.info(f"Alert info: {json.dumps(self.alert_info)}")
        
        # 调用 minio_update 和 alarm_info_post
        # self.call_minio_and_alarm(alert_image_path, alert_video_path)
        
    
    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):
        # 调用 minio_update
        logging.info(f"Uploading image to MinIO: {alert_image_path}")
        return_image_name = minio_update.minio_interface(
            self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
        )
        logging.info(f"Image uploaded to MinIO: {return_image_name}")
        
        logging.info(f"Uploading video to MinIO: {alert_video_path}")
        return_video_name = minio_update.minio_interface(
            self.dict_json, "clip", os.path.basename(alert_video_path), alert_video_path
        )
        logging.info(f"Video uploaded to MinIO: {return_video_name}")
        
        # 调用 alarm_info_post
        logging.info(f"Posting alarm info to server")
        
        self.drone_state = drone_state_get(self.dict_json)

        drones_server.alarm_info_post(
            active_drone=self.dict_json,
            drone_state=self.drone_state,
            classes=alarmClass,  # 告警类别
            alarmLevel=alarm_level,  # 告警级别
            alarmImageUrl=return_image_name,  # 告警图片路径
            videoUrl=return_video_name  # 告警视频路径
        )
        logging.info(f"Alarm info posted to server")

    # @staticmethod
    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):
        command = [
            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',
            '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-crf', '23', alert_video_path
        ]
        try:
            pipe = subprocess.Popen(command, stdin=subprocess.PIPE)
            for frame in frames:
                pipe.stdin.write(frame.tobytes())
            pipe.stdin.close()
            pipe.wait()
            logging.info(f"Alert video saved: {alert_video_path}")
            
            # 异步调用 minio_update.minio_interface
            threading.Thread(
                target=self.call_minio_and_alarm, 
                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)
            ).start()
        except Exception as e:
            logging.error(f"Failed to save alert video: {e}")   

                
                
    def pushImg(self):
        try:
            frame_count = 0  # 帧计数器
            frame_interval = 1 / 30  # 目标帧率 30fps，每帧间隔 0.033 秒
            last_frame_time = time.time()  # 记录上一帧的时间
            
            while self.is_switchOn:
                t_start = time.time()
                
                # 从队列中获取帧
                if not self.new_queue.empty():
                    frame = self.new_queue.get()
                else:
                    time.sleep(0.001)  # 避免空队列时 CPU 占用过高
                    continue
                
                # 绘制检测框
                if self.results and self.dataT < 0.2:
                    for result in self.results:
                        for box in result.boxes:
                            # 获取框坐标
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            # 获取类别名称
                            clss = int(box.cls)
                            if clss == 2:
                                if self.drone_angle > -1 and self.drone_angle < 1:
                                    if self.base_points[1] < center_x < self.base_points[2]:  # 在虚线之间
                                        alarmLevel = "1"
                                    elif self.base_points[0] < center_x < self.base_points[1] or self.base_points[2] < center_x < self.base_points[3]:                                
                                        alarmLevel = "2"  
                                    else:
                                        alarmLevel = "0"
                                else:
                                    center1, center2, center3, center4, radius = self.calculate_centers(self.drone_angle, self.base_points, frame.shape[0])
                                        
                                    alarmLevel = self.is_point_between_curves((center_x, center_y), (center1, center2, center3, center4), radius)
                                    
                                if alarmLevel == "1":  # 在虚线之间
                                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                                elif alarmLevel == "2":
                                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 2)
                            else:
                                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 推流
                try:
                    self.pipe.stdin.write(frame.tobytes())
                except Exception as e:
                    logging.error(f"Failed to write frame to pipe: {e}")
                    break
                
                # 计算处理时间
                elapsed = time.time() - t_start
                frame_count += 1
                
                # 每100帧打印一次平均处理时间
                if frame_count % 100 == 0:
                    logging.info(f"push time =: {elapsed}")
                
                # 精确帧率控制
                next_frame_time = last_frame_time + frame_interval
                sleep_time = max(0, next_frame_time - time.time())
                time.sleep(sleep_time)
                
                final_push_time = time.time() -  t_start                 
                if frame_count % 100 == 0:
                    logging.info(f"final time =: {final_push_time}")
                
                        
        except Exception as e:
            logging.error(f"Streaming error: {e}")
        finally:
            try:
                self.pipe.stdin.close()
                self.pipe.wait()
            except Exception as e:
                logging.error(f"Failed to close pipe: {e}")
                
    
                

    def saveImg(self):
        try:
            while self.is_switchOn or not self.save_queue.empty():
                if not self.save_queue.empty():
                    frame = self.save_queue.get()
                    try:
                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                    except Exception as e:
                        logging.error(f"Failed to write frame to save video: {e}")
                        break
                else:
                    time.sleep(0.01)  # 避免空队列时 CPU 占用过高
    
            # 视频保存完成后，上传到 MinIO
            if os.path.exists(self.save_full_video_path):
                return_video_name = minio_update.minio_interface(
                    self.dict_json, 
                    "full", 
                    os.path.basename(self.save_full_video_path), 
                    self.save_full_video_path
                )
                logging.info(f"Full video uploaded to MinIO: {return_video_name}")
            else:
                logging.error(f"Full video file not found: {self.save_full_video_path}")
    
        except Exception as e:
            logging.error(f"Video save error: {e}")
        finally:
            self.ffmpegSaveVideo.stdin.close()
            self.ffmpegSaveVideo.wait()
            logging.info("Video save process completed.")

        
    def set_switch_on(self, listenVule):
        self.is_switchOn = listenVule
        
    def set_video_name(self, video_name):
        self.video_name = video_name

    def set_mission_id(self, mission_id):
        self.mission_id = mission_id

    def _listen_for_stop(self):
        while self.is_switchOn:
            user_input = input("输入 'stop' 关闭视频流: ")
            if user_input.strip().lower() == 'stop':
                self.is_switchOn = False
                logging.info("收到关闭信号，正在关闭视频流...")
                break
            
    def listen_for_alert(self):
        while self.is_switchOn:
            if self.alert_flag:
                # 当 alert_flag 为 True 时执行特定操作
                self.handle_alert()
                self.alert_flag = False  # 重置 alert_flag
            time.sleep(0.1)  # 每 0.1 秒检查一次
    def handle_alert(self):
        # 在这里实现当 alert_flag 为 True 时需要执行的操作
        logging.info("Alert detected! Handling alert...")
        self.drone_state = drone_state_get(self.dict_json)
                
    
    ###############################################################################################
    ###############################################################################################
    #画无人机检测区域线
    def draw_smooth_curve(self, image, center, radius, color):
        height, width = image.shape[:2]
        cx, cy = center
        
        # 计算与上边y=0的交点
        y_top = 0
        discriminant_top = radius**2 - (y_top - cy)**2
        top_points = []
        if discriminant_top >= 0:
            sqrt_disc_top = np.sqrt(discriminant_top)
            for dx in [-sqrt_disc_top, sqrt_disc_top]:
                x = cx + dx
                if 0 <= x < width:
                    top_points.append((x, y_top))
        
        # 计算与下边y=height-1的交点
        y_bottom = height - 1
        discriminant_bottom = radius**2 - (y_bottom - cy)**2
        bottom_points = []
        if discriminant_bottom >= 0:
            sqrt_disc_bottom = np.sqrt(discriminant_bottom)
            for dx in [-sqrt_disc_bottom, sqrt_disc_bottom]:
                x = cx + dx
                if 0 <= x < width:
                    bottom_points.append((x, y_bottom))
        
        # 计算与左边x=0的交点
        x_left = 0
        discriminant_left = radius**2 - (x_left - cx)**2
        left_points = []
        if discriminant_left >= 0:
            sqrt_disc_left = np.sqrt(discriminant_left)
            for dy in [-sqrt_disc_left, sqrt_disc_left]:
                y = cy + dy
                if 0 <= y < height:
                    left_points.append((x_left, y))
        
        # 计算与右边x=width-1的交点
        x_right = width - 1
        discriminant_right = radius**2 - (x_right - cx)**2
        right_points = []
        if discriminant_right >= 0:
            sqrt_disc_right = np.sqrt(discriminant_right)
            for dy in [-sqrt_disc_right, sqrt_disc_right]:
                y = cy + dy
                if 0 <= y < height:
                    right_points.append((x_right, y))
        
        # 收集所有交点
        all_points = top_points + bottom_points + left_points + right_points
        if len(all_points) < 2:
            return image  # 不足以绘制圆弧
        
        # 按x坐标排序，选择最左和最右的交点
        sorted_points = sorted(all_points, key=lambda p: p[0])
        start_point, end_point = sorted_points[0], sorted_points[-1]
        
        # 计算角度（转换为0-360度）
        def calculate_angle(x, y):
            dx, dy = x - cx, y - cy
            angle = np.degrees(np.arctan2(dy, dx)) % 360
            return angle
        
        start_angle = calculate_angle(*start_point)
        end_angle = calculate_angle(*end_point)
        
        # 确保绘制较大的圆弧
        if abs(end_angle - start_angle) < 180:
            start_angle, end_angle = end_angle, start_angle
        
        # 绘制圆弧
        cv2.ellipse(
            image,
            (int(cx), int(cy)),
            (int(radius), int(radius)),
            0,
            start_angle,
            end_angle,
            color,
            2,
            lineType=cv2.LINE_AA  # 抗锯齿
        )
        
        return image
    
    def draw_smooth_curve1(self, image, center, radius, color):
        height, width = image.shape[:2]
        cx, cy = center
        
        # 计算与上边y=0的交点
        y_top = 0
        discriminant_top = radius**2 - (y_top - cy)**2
        top_points = []
        if discriminant_top >= 0:
            sqrt_disc_top = np.sqrt(discriminant_top)
            for dx in [-sqrt_disc_top, sqrt_disc_top]:
                x = cx + dx
                if 0 <= x < width:
                    top_points.append((x, y_top))
        
        # 计算与下边y=height-1的交点
        y_bottom = height - 1
        discriminant_bottom = radius**2 - (y_bottom - cy)**2
        bottom_points = []
        if discriminant_bottom >= 0:
            sqrt_disc_bottom = np.sqrt(discriminant_bottom)
            for dx in [-sqrt_disc_bottom, sqrt_disc_bottom]:
                x = cx + dx
                if 0 <= x < width:
                    bottom_points.append((x, y_bottom))
        
        # 计算与左边x=0的交点
        x_left = 0
        discriminant_left = radius**2 - (x_left - cx)**2
        left_points = []
        if discriminant_left >= 0:
            sqrt_disc_left = np.sqrt(discriminant_left)
            for dy in [-sqrt_disc_left, sqrt_disc_left]:
                y = cy + dy
                if 0 <= y < height:
                    left_points.append((x_left, y))
        
        # 计算与右边x=width-1的交点
        x_right = width - 1
        discriminant_right = radius**2 - (x_right - cx)**2
        right_points = []
        if discriminant_right >= 0:
            sqrt_disc_right = np.sqrt(discriminant_right)
            for dy in [-sqrt_disc_right, sqrt_disc_right]:
                y = cy + dy
                if 0 <= y < height:
                    right_points.append((x_right, y))
        
        # 收集所有交点
        all_points = top_points + bottom_points + left_points + right_points
        if len(all_points) < 2:
            return image  # 不足以绘制圆弧
        
        # 按x坐标排序，选择最左和最右的交点
        sorted_points = sorted(all_points, key=lambda p: p[0])
        start_point, end_point = sorted_points[0], sorted_points[-1]
        
        # 计算角度（转换为0-360度）
        def calculate_angle(x, y):
            dx, dy = x - cx, y - cy
            angle = np.degrees(np.arctan2(dy, dx)) % 360
            return angle
        
        start_angle = calculate_angle(*start_point)
        end_angle = calculate_angle(*end_point)
        
        # 确保绘制较大的圆弧
        if abs(end_angle - start_angle) < 180:
            start_angle, end_angle = end_angle, start_angle
        
        # 计算圆弧的总长度
        arc_length = 2 * np.pi * radius * (abs(end_angle - start_angle) / 360)
        
        # 定义虚线的每段长度和间隔
        dash_length = 10
        gap_length = 5
        
        # 分段绘制虚线
        current_length = 0
        while current_length < arc_length:
            # 计算当前段的起始和结束角度
            start_ratio = current_length / arc_length
            end_ratio = (current_length + dash_length) / arc_length
            current_start_angle = start_angle + (end_angle - start_angle) * start_ratio
            current_end_angle = start_angle + (end_angle - start_angle) * end_ratio
            
            # 绘制当前段
            cv2.ellipse(
                image,
                (int(cx), int(cy)),
                (int(radius), int(radius)),
                0,
                current_start_angle,
                current_end_angle,
                color,
                2,
                lineType=cv2.LINE_AA  # 抗锯齿
            )
            
            # 更新当前长度
            current_length += dash_length + gap_length
        
        return image
    
    def calculate_centers(self, angle, base_points, image_height):
        """
        计算四个圆心的坐标
        angle: 角度（可正可负）
        base_points: 底部四个交叉点 (x1, x2, x3, x4)
        image_height: 图像高度
        返回: 
            center1, center2, center3, center4: 四个圆心坐标
            radius: 基础半径
        """
        x1, x2, x3, x4 = base_points
        
        # 基础半径（可根据需要调整）
        base_radius = 6000
        
        # 角度影响半径的系数
        k_radius = 60
        
        # 实际半径 = 基础半径 - |角度| * 系数
        radius = base_radius - abs(angle) * k_radius
        
        if angle < 0:
            # 左偏曲线：圆心在左侧
            center1 = (x1 - radius, image_height)
            center2 = (x2 - radius, image_height)
            center3 = (x3 - radius, image_height)
            center4 = (x4 - radius, image_height)
        else:
            # 右偏曲线：圆心在右侧
            center1 = (x1 + radius, image_height)
            center2 = (x2 + radius, image_height)
            center3 = (x3 + radius, image_height)
            center4 = (x4 + radius, image_height)
        
        return center1, center2, center3, center4, radius
    
    def is_point_between_curves(self, point, centers, radius):
        """
        判断点位于哪两条曲线之间
        point: 给定点的坐标 (x, y)
        centers: 四个圆心坐标 (center1, center2, center3, center4)
        radius: 曲线的半径
        返回: 
            1: 点位于内侧两根线中
            2: 点位于红黄之间
            0: 点位于其他位置
        """
        x, y = point
        center1, center2, center3, center4 = centers
        
        # 计算点到每个圆心的距离
        def distance_to_center(center):
            cx, cy = center
            return np.sqrt((x - cx)**2 + (y - cy)**2)
        
        d1 = distance_to_center(center1)
        d2 = distance_to_center(center2)
        d3 = distance_to_center(center3)
        d4 = distance_to_center(center4)
        # print("d1 = ", d1)
        # print("d2 = ", d2)
        # print("d3 = ", d3)
        # print("d4 = ", d4)
        
        # 判断点的位置
        if d2 < radius and d3 > radius:
            return "1"  # 点位于内侧两根线中
        elif (d1 < radius and d2 > radius) or (d3 < radius and d4 > radius):
            return "2"  # 点位于红黄之间
        else:
            return "0"  # 点位于其他位置
        
    def draw_all_Curve(self, image, angle, base_points):
        
        if image is None:
            image = np.zeros((720, 1280, 3), dtype=np.uint8)  # 备用：创建黑色图像
        
        # logging.info("#############################")
        # 计算圆心和半径
        center1, center2, center3, center4, radius = self.calculate_centers(angle, base_points, image.shape[0])
        
        # 绘制圆弧（四条曲线）
        result = image.copy()
        # 第一组圆心：两条曲线
        result = self.draw_smooth_curve(result, center1, radius, (0, 255, 255))         # 红色
        result = self.draw_smooth_curve(result, center2, radius, (0, 0, 255))      # 黄色
        # 第二组圆心：两条曲线
        result = self.draw_smooth_curve(result, center3, radius, (0, 0, 255))      # 黄色
        result = self.draw_smooth_curve(result, center4, radius, (0, 255, 255))        # 红色
        
        return result
    
    ###############################################################################################
    ###############################################################################################

    def startThread1(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg)
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")
            
    def check_pipe_health(self):
        while self.is_switchOn:
            if self.pipe.poll() is not None:  # 检查 FFmpeg 进程是否已退出
                logging.error("FFmpeg process has exited unexpectedly.")
                self.is_switchOn = False
                break
            time.sleep(5)  # 每5秒检查一次
            
    def startThread(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg),
                threading.Thread(target=self.check_pipe_health)  # 新增健康检查线程
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except KeyboardInterrupt:
            logging.info("Received keyboard interrupt, stopping threads...")
            self.is_switchOn = False
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")

class StreamManager:
    def __init__(self, model_path):
        self.model_path = model_path
        self.streams = []
        self.lock = multiprocessing.Lock()
        self.processor = VideoStreamProcessor(model_path)
        self.active_processes = {}  # 用于存储活动的进程
        # self.active_streams = {}  # 用于记录正在运行的流地址

    def add_stream(self, dict_json, out_res):
        input_path = dict_json["droneStreamUrl"]
        
        output_url = dict_json["aiStreamUrl"]
        # input_path = "rtmp://************/stream"
        # output_url = "rtmp://***************:1936/live3/stream4"
        video_name = dict_json["taskId"]
        
        with self.lock:
            # 检查输入流是否已经启动
            # if input_path in self.active_streams:
            #     logging.warning(f"Stream {input_path} is already running.")
            #     return
            
            # 创建以 video_name 命名的文件夹
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            # 设置保存路径
            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            # 启动新流的处理进程
            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path)
            )
            process.start()
            
            # 存储进程和流地址
            self.active_processes[input_path] = process
            # self.active_streams[input_path] = True
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                # 设置流的开关为 False
                self.processor.stop_stream(input_url)
                process.join()  # 等待进程结束
                del self.active_processes[input_url]  # 从活动进程中移除
                del self.active_streams[input_url]  # 从活动流地址中移除
                logging.info(f"Stream {input_url} has been stopped.")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        with self.lock:
            return self.streams

    def is_stream_active(self, output_url):
        with self.lock:
            return output_url in self.active_streams


class VideoStreamProcessor:
    
    def __init__(self, model_path):
        self.model_path = model_path
        self.active_detectors = {}  # 用于存储活动的检测器
        self.lock = multiprocessing.Lock()  # 用于进程安全的操作

    def process_stream(self, dict_json, save_path):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        # input_path = "rtmp://************/stream"
        # output_url = "rtmp://***************:1936/live3/stream4"
        # video_name = dict_json["taskId"]
        
        
        detector = multiDealImg(self.model_path, save_path, dict_json)
        detector.set_switch_on(True)
        detector.set_video_name(video_name)
        
        with self.lock:
            self.active_detectors[input_path] = detector  # 存储检测器
        try:
            detector.startThread(input_path, output_url, save_path)
        finally:
            # 流处理结束后，清理资源
            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]  # 从活动检测器中移除
            logging.info(f"Stream {input_path} has finished and been removed from active streams.")
            

    
    def is_detector_active(self, input_path):
        with self.lock:
            return input_path in self.active_detectors
        
    def stop_stream(self, input_path):
        if input_path in self.active_detectors:
            detector = self.active_detectors[input_path]
            detector.set_switch_on(False)  # 设置流的开关为 False
            del self.active_detectors[input_path]  # 
            logging.info(f"Detector for stream {input_path} has been stopped.")
        else:
            logging.warning(f"Detector for stream {input_path} not found.")
                
    def process_streams(self, streams):
        processes = []
        for stream in streams:
            process = multiprocessing.Process(
                target=self.process_stream,
                args=(stream["input"], stream["output"], stream["save_path"], stream["video_name"])
            )
            process.start()
            processes.append(process)

        for process in processes:
            process.join()

    def process_streams_multiprocess(self, streams):
        processes = []
        for stream in streams:
            process = multiprocessing.Process(
                target=self.process_stream,
                args=(stream["input"], stream["output"], stream["save_path"], stream["video_name"])
            )
            process.start()
            processes.append(process)

        for process in processes:
            process.join()

# 定义比较函数，基于指定的关键字 
def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)

if __name__ == "__main__":

    
    setup_logging()
    #scheduler = BlockingScheduler()
    # model_path = "./model/best0701_640_2.pt"
    model_path = "./model/best_cj.pt"
    out_res = "./res"
    stream_manager = StreamManager(model_path)  
    
    # scheduler.add_job(drones_interface_run, 'interval', seconds=300) 
    #scheduler.start()
    # 主循环保持程序运行
    
    # 定义关键字列表
    keys = ["droneStreamUrl"]
    
    # active_drones_list = [
    #     {
    #         "droneDeviceSn": "1581F6Q8X24BJ00G011E",
    #         "area": "芜湖轨道机巢",
    #         "monitorEq": "芜湖轨道机巢无人机",
    #         "aiVendorInfo": {
    #             "id": "1",
    #             "aiAlgorithmTypes": "[]"
    #         },
    #         "droneFlightMode": "",
    #         "droneStreamUrl": "rtmp://************/stream",
    #         # "droneStreamUrl": "F:/datasets/video/厦门巡检/20250630/0630_5.mp4",
    #         "rootName": "中山北路",
    #         "gatewayDeviceSn": "7CTXMA600B02VF",
    #         "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
    #         "taskId": "test33",
    #         "aiStreamUrl":"rtmp://************/stream1",
    #         "timestamp": "1750069019242"
    #     }
    # ]
    
    presentStream = []
    while True:
        # drones_interface_run()
        #获取token
        login_token_get()
        #获取正在飞行任务中的无人机列表
        active_drones_list = active_drones_get()
        # 获取 active_drones_list 中有且 presentStream 中没有的元素
        drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
        
        # print("active_streams = ", stream_manager.active_streams)
        if(len(drones_unique) > 0):
            for active_drone in drones_unique:        
              stream_manager.add_stream(active_drone, out_res)
        else:
            print("active_processes = ", presentStream)
            stream_manager.active_processes = {}
        presentStream = active_drones_list
        
        time.sleep(15)
    


    
    
    
    
    

        
