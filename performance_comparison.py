"""
性能对比脚本
用于测试原版本和优化版本的性能差异
"""

import time
import threading
import queue
import cv2
import logging
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance_comparison.log'),
        logging.StreamHandler()
    ]
)

class PerformanceMonitor:
    def __init__(self, name):
        self.name = name
        self.start_time = None
        self.frame_count = 0
        self.total_inference_time = 0
        self.total_process_time = 0
        
    def start(self):
        self.start_time = time.time()
        self.frame_count = 0
        self.total_inference_time = 0
        self.total_process_time = 0
        
    def record_frame(self, inference_time=0, process_time=0):
        self.frame_count += 1
        self.total_inference_time += inference_time
        self.total_process_time += process_time
        
    def get_stats(self):
        if self.start_time is None:
            return None
            
        elapsed = time.time() - self.start_time
        avg_fps = self.frame_count / elapsed if elapsed > 0 else 0
        avg_inference = self.total_inference_time / self.frame_count if self.frame_count > 0 else 0
        avg_process = self.total_process_time / self.frame_count if self.frame_count > 0 else 0
        
        return {
            'name': self.name,
            'elapsed_time': elapsed,
            'frame_count': self.frame_count,
            'avg_fps': avg_fps,
            'avg_inference_time': avg_inference,
            'avg_process_time': avg_process,
            'total_inference_time': self.total_inference_time,
            'total_process_time': self.total_process_time
        }

class MockInference:
    """模拟推理，支持不同的推理策略"""
    
    def __init__(self, strategy='original'):
        self.strategy = strategy
        
    def infer(self, frame, frame_count):
        if self.strategy == 'original':
            # 原版本：每帧都推理，使用原分辨率
            time.sleep(0.05)  # 模拟50ms推理时间 (1920x1080)
            return True
        elif self.strategy == 'optimized':
            # 优化版本：跳帧推理，使用低分辨率
            if frame_count % 2 == 0:  # 每2帧推理一次
                time.sleep(0.02)  # 模拟20ms推理时间 (640x360)
                return True
            return False

def test_original_approach(video_path, duration=30):
    """测试原版本方法"""
    monitor = PerformanceMonitor("Original")
    inference = MockInference('original')
    
    # 创建队列（原版本使用较大队列）
    frame_queue = queue.Queue(maxsize=10)
    push_queue = queue.LifoQueue(maxsize=3)
    save_queue = queue.Queue(maxsize=10)
    
    is_running = True
    
    def read_video():
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logging.error(f"无法打开视频: {video_path}")
            return
            
        while is_running:
            ret, frame = cap.read()
            if not ret:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 循环播放
                continue
                
            frame = cv2.resize(frame, (1920, 1080))
            
            if not frame_queue.full():
                frame_queue.put(frame)
            # 原版本：无跳帧处理
                
        cap.release()
    
    def process_frame():
        frame_count = 0
        while is_running:
            try:
                frame = frame_queue.get(timeout=1.0)
            except queue.Empty:
                continue
                
            t1 = time.time()
            
            # 原版本：每帧都推理
            inference_start = time.time()
            did_infer = inference.infer(frame, frame_count)
            inference_time = time.time() - inference_start
            
            # 模拟复杂的告警检测
            time.sleep(0.01)  # 10ms
            
            # 原版本：频繁的帧复制
            display_frame = frame.copy()  # 复制1
            save_frame = frame.copy()     # 复制2
            
            # 模拟轨迹线绘制
            time.sleep(0.005)  # 5ms
            
            # 放入队列
            try:
                push_queue.put(display_frame, timeout=0.1)
                save_queue.put(save_frame, timeout=0.1)
            except queue.Full:
                pass
            
            process_time = time.time() - t1
            monitor.record_frame(inference_time, process_time)
            frame_count += 1
    
    def push_stream():
        while is_running:
            try:
                frame = push_queue.get(timeout=1.0)
                # 模拟推流处理
                time.sleep(0.033)  # 33ms (30fps)
            except queue.Empty:
                continue
    
    def save_video():
        while is_running:
            try:
                frame = save_queue.get(timeout=1.0)
                # 模拟保存处理
                time.sleep(0.01)  # 10ms
            except queue.Empty:
                continue
    
    # 启动所有线程
    monitor.start()
    threads = [
        threading.Thread(target=read_video),
        threading.Thread(target=process_frame),
        threading.Thread(target=push_stream),
        threading.Thread(target=save_video)
    ]
    
    for t in threads:
        t.daemon = True
        t.start()
    
    # 运行指定时间
    time.sleep(duration)
    is_running = False
    
    # 等待线程结束
    for t in threads:
        t.join(timeout=2)
    
    return monitor.get_stats()

def test_optimized_approach(video_path, duration=30):
    """测试优化版本方法"""
    monitor = PerformanceMonitor("Optimized")
    inference = MockInference('optimized')
    
    # 创建队列（优化版本使用较小队列）
    frame_queue = queue.Queue(maxsize=2)
    push_queue = queue.Queue(maxsize=2)  # 改为FIFO
    save_queue = queue.Queue(maxsize=5)
    
    is_running = True
    
    def read_video():
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logging.error(f"无法打开视频: {video_path}")
            return
            
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲
        skip_frames = 0
        
        while is_running:
            ret, frame = cap.read()
            if not ret:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            
            # 优化版本：跳帧处理
            skip_frames += 1
            if skip_frames % 3 != 0:
                continue
                
            frame = cv2.resize(frame, (1920, 1080))
            
            # 非阻塞队列操作
            try:
                frame_queue.put_nowait(frame)
            except queue.Full:
                try:
                    frame_queue.get_nowait()  # 丢弃旧帧
                    frame_queue.put_nowait(frame)
                except queue.Empty:
                    pass
                
        cap.release()
    
    def process_frame():
        frame_count = 0
        last_results = None
        
        while is_running:
            try:
                frame = frame_queue.get(timeout=1.0)
            except queue.Empty:
                continue
                
            t1 = time.time()
            
            # 优化版本：控制推理频率
            inference_start = time.time()
            did_infer = inference.infer(frame, frame_count)
            inference_time = time.time() - inference_start if did_infer else 0
            
            # 简化的告警检测
            time.sleep(0.005)  # 5ms
            
            # 优化版本：减少帧复制
            display_frame = frame.copy()  # 只复制一次
            
            # 减少轨迹线绘制频率
            if frame_count % 5 == 0:
                time.sleep(0.003)  # 3ms
            
            # 非阻塞队列操作
            try:
                save_queue.put_nowait(frame)  # 直接使用原帧
            except queue.Full:
                pass
                
            try:
                push_queue.put_nowait(display_frame)
            except queue.Full:
                try:
                    push_queue.get_nowait()
                    push_queue.put_nowait(display_frame)
                except queue.Empty:
                    pass
            
            process_time = time.time() - t1
            monitor.record_frame(inference_time, process_time)
            frame_count += 1
    
    def push_stream():
        while is_running:
            try:
                frame = push_queue.get(timeout=0.1)
                # 优化的推流处理
                time.sleep(0.025)  # 25ms (25fps)
            except queue.Empty:
                continue
    
    def save_video():
        while is_running:
            try:
                frame = save_queue.get(timeout=1.0)
                # 优化的保存处理
                time.sleep(0.005)  # 5ms
            except queue.Empty:
                continue
    
    # 启动所有线程
    monitor.start()
    threads = [
        threading.Thread(target=read_video),
        threading.Thread(target=process_frame),
        threading.Thread(target=push_stream),
        threading.Thread(target=save_video)
    ]
    
    for t in threads:
        t.daemon = True
        t.start()
    
    # 运行指定时间
    time.sleep(duration)
    is_running = False
    
    # 等待线程结束
    for t in threads:
        t.join(timeout=2)
    
    return monitor.get_stats()

def compare_performance(video_path, duration=30):
    """性能对比"""
    print(f"开始性能对比测试 (时长: {duration}秒)")
    print(f"测试视频: {video_path}")
    print("-" * 60)
    
    # 测试原版本
    print("正在测试原版本...")
    original_stats = test_original_approach(video_path, duration)
    
    print("等待5秒后开始优化版本测试...")
    time.sleep(5)
    
    # 测试优化版本
    print("正在测试优化版本...")
    optimized_stats = test_optimized_approach(video_path, duration)
    
    # 输出结果
    print("\n" + "=" * 80)
    print("性能对比结果")
    print("=" * 80)
    
    def print_stats(stats):
        if stats:
            print(f"版本: {stats['name']}")
            print(f"运行时长: {stats['elapsed_time']:.2f}s")
            print(f"处理帧数: {stats['frame_count']}")
            print(f"平均FPS: {stats['avg_fps']:.2f}")
            print(f"平均推理时间: {stats['avg_inference_time']*1000:.2f}ms")
            print(f"平均处理时间: {stats['avg_process_time']*1000:.2f}ms")
            print(f"推理时间占比: {(stats['total_inference_time']/stats['elapsed_time']*100):.1f}%")
            print(f"处理时间占比: {(stats['total_process_time']/stats['elapsed_time']*100):.1f}%")
        else:
            print("无统计数据")
    
    print_stats(original_stats)
    print("-" * 40)
    print_stats(optimized_stats)
    
    if original_stats and optimized_stats:
        print("\n" + "=" * 40)
        print("改进效果")
        print("=" * 40)
        fps_improvement = ((optimized_stats['avg_fps'] - original_stats['avg_fps']) 
                          / original_stats['avg_fps'] * 100)
        process_improvement = ((original_stats['avg_process_time'] - optimized_stats['avg_process_time']) 
                             / original_stats['avg_process_time'] * 100)
        
        print(f"FPS提升: {fps_improvement:+.1f}%")
        print(f"处理时间优化: {process_improvement:+.1f}%")
        
        if fps_improvement > 0:
            print("✓ FPS得到提升")
        else:
            print("✗ FPS未得到提升")
            
        if process_improvement > 0:
            print("✓ 处理时间得到优化")
        else:
            print("✗ 处理时间未得到优化")

if __name__ == "__main__":
    # 请设置你的测试视频路径
    video_path = r"D:\crscu\object_detection\result3.mp4"  # 请替换为实际的视频文件路径
    
    # 检查视频文件是否存在
    import os
    if not os.path.exists(video_path):
        print(f"错误: 找不到视频文件 {video_path}")
        print("请将测试视频命名为 test_video.mp4 并放在当前目录")
        print("或者修改 video_path 变量为实际的视频文件路径")
        exit(1)
    
    # 开始性能对比（测试30秒）
    compare_performance(video_path, duration=30)
