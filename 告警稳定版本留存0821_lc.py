# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS
"""

import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque
from enum import Enum

from ultralytics import YOLO
import subprocess
from ultralytics.utils.plotting import Annotator, colors, save_one_box
import logging
import json
import torch

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math
import sys
from apscheduler.schedulers.blocking import BlockingScheduler

logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class AlgorithmMode(Enum):
    """算法模式枚举"""
    TRACKING_ONLY = "tracking_only"  # 仅目标追踪
    SEGMENTATION_ONLY = "segmentation_only"  # 仅分割
    HYBRID = "hybrid"  # 混合模式（同时运行两种算法）

class HybridConfig:
    """混合算法配置类"""
    def __init__(self):
        # 算法模式配置
        self.algorithm_mode = AlgorithmMode.HYBRID  # 默认混合模式
        
        # 模型路径配置
        self.tracking_model_path = "/home/<USER>/suanfa/czy/vis-m-bs16-sz960.pt"
        self.segmentation_model_path = "/home/<USER>/suanfa/czy/water.pt"
        
        # 类别配置
        self.tracking_classes = ["None", "people", "bicycle", "car", "van", "truck", "tricycle", "awning-tricycle", "bus", "motor"]
        self.segmentation_classes = ["water"]
        
        # 性能配置 - 统一推理间隔，实现同步融合
        self.tracking_conf_threshold = 0.7
        self.segmentation_conf_threshold = 0.5
        self.tracking_inference_interval = 2  # 追踪算法推理间隔（帧数）
        self.segmentation_inference_interval = 2  # 分割算法推理间隔（帧数）- 与追踪保持一致

        # 异步执行配置 - 启用异步推理并等待完整结果
        self.enable_async_inference = True  # 启用异步推理，并行执行追踪和分割
        self.max_inference_threads = 2  # 最大推理线程数
        self.async_wait_timeout = 0.1  # 等待异步结果的超时时间（秒）

        # 结果融合配置
        self.enable_result_fusion = True  # 启用结果融合
        self.fusion_alpha = 0.7  # 融合透明度

        # 推流优化配置 - 确保检测结果连续显示
        self.prioritize_original_stream = False  # 始终优先显示检测结果
        self.detection_overlay_ratio = 1.0  # 检测结果叠加比例（0.0-1.0）- 设为1.0显示所有检测框
        self.stream_quality_priority = False  # 优先显示检测结果而非推流质量
        
        # 智能负载均衡配置
        self.adaptive_quality = True  # 启用自适应质量调整
        self.max_targets_threshold = 15  # 目标数量阈值，超过此数量时启用性能优化
        self.performance_mode = False  # 性能模式标志
        
        # 告警控制参数
        self.alarm_target_threshold = 2  # 告警目标数量阈值，当追踪目标数量超过此阈值时才触发告警
        self.alarm_interval_seconds = 2.0  # 告警间隔时间（秒），避免重复告警
        
    def is_tracking_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.TRACKING_ONLY, AlgorithmMode.HYBRID]
    
    def is_segmentation_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.SEGMENTATION_ONLY, AlgorithmMode.HYBRID]

class HybridMultiDealImg(object):
    """融合目标追踪和分割的混合检测类"""
    tracking_model = None  # 追踪模型类变量
    segmentation_model = None  # 分割模型类变量
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, config: HybridConfig, save_path, dict_json):
        self.config = config
        
        # 从配置中获取类别信息
        self.tracking_classes = config.tracking_classes
        self.segmentation_classes = config.segmentation_classes
        
        # 初始化模型
        if self.config.is_tracking_enabled() and HybridMultiDealImg.tracking_model is None:
            HybridMultiDealImg.tracking_model = YOLO(self.config.tracking_model_path).to("cuda")
        if self.config.is_segmentation_enabled() and HybridMultiDealImg.segmentation_model is None:
            HybridMultiDealImg.segmentation_model = YOLO(self.config.segmentation_model_path).to("cuda")
        
        self.tracking_model = HybridMultiDealImg.tracking_model
        self.segmentation_model = HybridMultiDealImg.segmentation_model
        
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化队列设计 - 统一推流队列管理，避免帧类型混合
        self.save_queue = queue.Queue(maxsize=15)  # 保存队列
        self.push_queue = queue.Queue(maxsize=10)  # 统一推流队列，只存放最终显示帧
        self.frame_queue = queue.Queue(maxsize=15)  # 帧队列

        # 结果存储 - 增强结果缓存机制，确保检测框持续显示
        self.tracking_results = None
        self.segmentation_results = None
        self.last_valid_tracking_results = None  # 最后有效的追踪结果
        self.last_valid_segmentation_results = None  # 最后有效的分割结果
        self.tracking_result_valid_frames = 0  # 追踪结果有效帧计数
        self.segmentation_result_valid_frames = 0  # 分割结果有效帧计数
        self.max_result_valid_frames = 30  # 增加结果最大有效帧数，确保检测框持续显示

        # 新增：最后显示帧缓存，确保推流连续性
        self.last_display_frame = None
        self.last_display_frame_time = 0
        self.display_frame_timeout = 1.0  # 显示帧超时时间（秒）
        self.dataT = 2
        
        # 异步推理相关 - 使用直接Thread管理
        self.tracking_thread = None
        self.segmentation_thread = None
        self.tracking_inference_queue = queue.Queue(maxsize=2)  # 追踪推理任务队列
        self.segmentation_inference_queue = queue.Queue(maxsize=2)  # 分割推理任务队列
        self.tracking_result_queue = queue.Queue(maxsize=2)  # 追踪结果队列
        self.segmentation_result_queue = queue.Queue(maxsize=2)  # 分割结果队列
        
        # 启动异步推理线程
        if self.config.enable_async_inference:
            self._start_inference_threads()
        
        # 追踪算法相关（基于目标ID的跟踪）
        self.target_buffers = {}  # 为每个目标ID维护独立的帧缓存
        self.active_targets = set()  # 当前活跃的目标ID集合
        self.previous_targets = set()  # 上一帧的目标ID集合
        self.target_classes = {}  # 目标ID对应的类别
        self.alert_lock = threading.Lock()
        self.alert_info = {}  # 存储告警信息
        self.buffer_maxlen = 300  # 5秒缓存（30fps）

        # 出现即保存 8 秒片段 & 首图路径
        self.clip_seconds = 8
        self.clip_fps = 30
        # {target_id: {"frames": deque, "deadline": float, "class": str}}
        self.clip_jobs = {}
        self.writer_threads = []
        self.target_first_image_paths = {}  # {target_id: "/path/to/first.jpg"}
        
        # 追踪算法告警控制
        self.alerted_targets = set()  # 已告警的目标ID集合
        self.target_first_frames = {}  # 目标ID第一次出现时的帧
        self.disappeared_targets = set()  # 已经消失过的目标ID集合
        
        # 分割算法相关
        self.segmentation_alerted_targets = set()  # 分割算法已告警的检测结果集合
        self.frame_buffer = deque(maxlen=self.buffer_maxlen)  # 全局帧缓存
        
        self.drone_state = {}
        self.drone_angle = 0
        
        # 推理计数器 - 添加性能监控
        self.tracking_inference_count = 0
        self.segmentation_inference_count = 0
        self.current_target_count = 0  # 当前帧目标数量
        self.avg_target_count = 0  # 平均目标数量
        self.frame_processing_time = 0  # 帧处理时间
        self.performance_stats = {
            'frame_count': 0,
            'total_targets': 0,
            'processing_times': []
        }
        
        # 告警时间控制
        self.last_alarm_time = 0  # 上次告警时间，用于控制告警间隔
        
    def _start_inference_threads(self):
        """启动异步推理线程"""
        if self.config.is_tracking_enabled():
            self.tracking_thread = threading.Thread(target=self._tracking_worker, daemon=True)
            self.tracking_thread.start()
            logging.info("Tracking inference thread started")
            
        if self.config.is_segmentation_enabled():
            self.segmentation_thread = threading.Thread(target=self._segmentation_worker, daemon=True)
            self.segmentation_thread.start()
            logging.info("Segmentation inference thread started")
    
    def _tracking_worker(self):
        """追踪推理工作线程"""
        while True:
            try:
                # 获取推理任务
                frame = self.tracking_inference_queue.get(timeout=1.0)
                if frame is None:  # 停止信号
                    break
                    
                # 执行推理
                result = self._sync_tracking_inference(frame)
                
                # 将结果放入结果队列
                try:
                    self.tracking_result_queue.put_nowait((time.time(), result))
                except queue.Full:
                    # 如果队列满了，丢弃最旧的结果
                    try:
                        self.tracking_result_queue.get_nowait()
                        self.tracking_result_queue.put_nowait((time.time(), result))
                    except queue.Empty:
                        pass
                        
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Tracking worker error: {e}")
    
    def _segmentation_worker(self):
        """分割推理工作线程"""
        while True:
            try:
                # 获取推理任务
                frame = self.segmentation_inference_queue.get(timeout=1.0)
                if frame is None:  # 停止信号
                    break
                    
                # 执行推理
                result = self._sync_segmentation_inference(frame)
                
                # 将结果放入结果队列
                try:
                    self.segmentation_result_queue.put_nowait((time.time(), result))
                except queue.Full:
                    # 如果队列满了，丢弃最旧的结果
                    try:
                        self.segmentation_result_queue.get_nowait()
                        self.segmentation_result_queue.put_nowait((time.time(), result))
                    except queue.Empty:
                        pass
                        
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Segmentation worker error: {e}")

    def set_switch_on(self, switch_on):
        self.is_switchOn = switch_on
        # 如果关闭，停止推理线程
        if not switch_on and self.config.enable_async_inference:
            self._stop_inference_threads()

    def _stop_inference_threads(self):
        """停止异步推理线程"""
        try:
            # 发送停止信号
            if self.tracking_thread and self.tracking_thread.is_alive():
                self.tracking_inference_queue.put_nowait(None)
            if self.segmentation_thread and self.segmentation_thread.is_alive():
                self.segmentation_inference_queue.put_nowait(None)
        except queue.Full:
            pass

    def set_video_name(self, video_name):
        self.video_name = video_name

    def startThread(self, input_path, output_stream, save_path):
        """启动所有处理线程"""
        self.open_ffmpeg_process(output_stream)
        self.openFfmpegSaveVideo(save_path)
        
        # 启动视频读取线程
        read_thread = threading.Thread(target=self.read_video, args=(input_path,))
        read_thread.daemon = True
        read_thread.start()
        
        # 启动帧处理线程
        process_thread = threading.Thread(target=self.process_frame)
        process_thread.daemon = True
        process_thread.start()
        
        # 启动推流线程
        push_thread = threading.Thread(target=self.pushImg)
        push_thread.daemon = True
        push_thread.start()
        
        # 启动保存线程
        save_thread = threading.Thread(target=self.saveImg)
        save_thread.daemon = True
        save_thread.start()
        
        # 等待所有线程完成
        read_thread.join()
        process_thread.join()
        push_thread.join()
        save_thread.join()
        
        # 关闭FFmpeg进程
        if hasattr(self, 'pipe') and self.pipe:
            self.pipe.stdin.close()
            self.pipe.wait()
        if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:
            self.ffmpegSaveVideo.stdin.close()
            self.ffmpegSaveVideo.wait()
            
        # 关闭线程池
        if hasattr(self, 'inference_executor') and self.inference_executor:
            self.inference_executor.shutdown(wait=True)

    def open_ffmpeg_process(self, output_stream):
        """打开FFmpeg推流进程 - 优化编码参数以减少卡顿"""
        self.command = [
            'ffmpeg',
            '-y',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1280),
            '-r', '25',  # 输入帧率30fps
            '-i', '-',
            '-c:v', 'h264_nvenc',
            '-preset', 'p1',        # 最快预设
            '-rc', 'cbr',           # 恒定码率模式
            '-delay', '0',          # 零延迟
            '-g', '15',             # 减小GOP大小，提高实时性
            '-b:v', '4M',           # 增加码率支持更多目标
            '-maxrate', '6M',       # 提高最大码率
            '-bufsize', '2M',       # 增大缓冲区
            '-r', '25',             # 输出30fps
            '-pix_fmt', 'yuv420p',
            '-f', 'flv',
            output_stream
        ]
        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, bufsize=0)

    def openFfmpegSaveVideo(self, outputSaveVideo):
        """打开FFmpeg保存视频进程"""
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1280", "-r", "30", "-i", "-",
            "-c:v", "h264_nvenc", "-pix_fmt", "yuv420p", "-crf", "23", "-preset", "fast", outputSaveVideo
        ]
        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE, bufsize=0)

    def read_video(self, video_path):
        """读取视频流"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
    
        try:
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
    
                try:
                    frame = cv2.resize(frame, (1920, 1280))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 将帧放入处理队列
                if not self.frame_queue.full():
                    self.frame_queue.put(frame)
                else:
                    # 如果队列满了，移除最旧的帧以保持实时性
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put(frame)
                    except queue.Empty:
                        pass
                
                # 设置无人机角度
                # self.drone_angle = 0

                time.sleep(1.0/30)  # 控制读取速度为30fps
                
        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def _sync_tracking_inference(self, frame):
        """同步追踪推理（在工作线程中执行）"""
        try:
            with self.model_lock:
                if self.tracking_model:
                    results = self.tracking_model.track(frame, conf=self.config.tracking_conf_threshold, verbose=False, tracker='bytetrack.yaml')
                    return results
        except Exception as e:
            logging.error(f"Tracking inference error: {e}")
        return None

    def _sync_segmentation_inference(self, frame):
        """同步分割推理（在工作线程中执行）"""
        try:
            with self.model_lock:
                if self.segmentation_model:
                    results = self.segmentation_model.predict(frame, conf=self.config.segmentation_conf_threshold, verbose=False)
                    return results
        except Exception as e:
            logging.error(f"Segmentation inference error: {e}")
        return None
    
    def async_tracking_inference(self, frame):
        """异步追踪推理 - 提交任务到队列"""
        try:
            self.tracking_inference_queue.put_nowait(frame.copy())
        except queue.Full:
            # 如果队列满了，丢弃最旧的任务
            try:
                self.tracking_inference_queue.get_nowait()
                self.tracking_inference_queue.put_nowait(frame.copy())
            except queue.Empty:
                pass
    
    def async_segmentation_inference(self, frame):
        """异步分割推理 - 提交任务到队列"""
        try:
            self.segmentation_inference_queue.put_nowait(frame.copy())
        except queue.Full:
            # 如果队列满了，丢弃最旧的任务
            try:
                self.segmentation_inference_queue.get_nowait()
                self.segmentation_inference_queue.put_nowait(frame.copy())
            except queue.Empty:
                pass
    
    def get_latest_tracking_result(self):
        """获取最新的追踪推理结果"""
        latest_result = None
        latest_time = 0
        
        # 获取所有可用结果，保留最新的
        while True:
            try:
                timestamp, result = self.tracking_result_queue.get_nowait()
                if timestamp > latest_time:
                    latest_time = timestamp
                    latest_result = result
            except queue.Empty:
                break
        
        return latest_result

    def wait_for_tracking_result(self, timeout=None):
        """等待追踪推理结果完成"""
        if timeout is None:
            timeout = self.config.async_wait_timeout

        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.get_latest_tracking_result()
            if result is not None:
                return result
            time.sleep(0.001)  # 短暂等待，避免CPU占用过高
        return None

    def wait_for_segmentation_result(self, timeout=None):
        """等待分割推理结果完成"""
        if timeout is None:
            timeout = self.config.async_wait_timeout

        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.get_latest_segmentation_result()
            if result is not None:
                return result
            time.sleep(0.001)  # 短暂等待，避免CPU占用过高
        return None

    def get_latest_segmentation_result(self):
        """获取最新的分割推理结果"""
        latest_result = None
        latest_time = 0
        
        # 获取所有可用结果，保留最新的
        while True:
            try:
                timestamp, result = self.segmentation_result_queue.get_nowait()
                if timestamp > latest_time:
                    latest_time = timestamp
                    latest_result = result
            except queue.Empty:
                break
        
        return latest_result

    def process_frame(self):
        """处理帧的主函数"""
        try:
            count = 0

            # 推理结果缓存
            self.tracking_results = None
            self.segmentation_results = None
    
            while self.is_switchOn:
                try:
                    frame = self.frame_queue.get(timeout=1.0)
                    self._check_and_finalize_clips(None)
                except queue.Empty:
                    continue
                    
                t1 = time.time()
                
                # 创建显示帧用于视频收集（在推理之前）
                display_frame_for_collection = frame.copy()
                
                # 先将当前帧收集到所有活跃的视频任务中（确保每帧都被收集）
                self._check_and_finalize_clips(display_frame_for_collection)
                
                # 统一推理决策 - 追踪和分割同步进行
                should_inference = (count % self.config.tracking_inference_interval == 0)

                # 统一推理逻辑 - 同时进行追踪和分割推理
                if should_inference:
                    if self.config.enable_async_inference:
                        # 异步模式：同时提交两个推理任务
                        if self.config.is_tracking_enabled():
                            self.async_tracking_inference(frame)
                            self.tracking_inference_count += 1
                        if self.config.is_segmentation_enabled():
                            self.async_segmentation_inference(frame)
                            self.segmentation_inference_count += 1

                        # 等待两个推理结果完成
                        if self.config.is_tracking_enabled():
                            tracking_result = self.wait_for_tracking_result()
                            if tracking_result is not None:
                                self.tracking_results = tracking_result
                                self.last_valid_tracking_results = tracking_result
                                self.tracking_result_valid_frames = 0
                                logging.debug(f"Got new tracking result at frame {count}")

                        if self.config.is_segmentation_enabled():
                            segmentation_result = self.wait_for_segmentation_result()
                            if segmentation_result is not None:
                                self.segmentation_results = segmentation_result
                                self.last_valid_segmentation_results = segmentation_result
                                self.segmentation_result_valid_frames = 0
                                logging.debug(f"Got new segmentation result at frame {count}")
                    else:
                        # 同步模式：顺序执行两个推理任务
                        if self.config.is_tracking_enabled():
                            self.tracking_results = self._sync_tracking_inference(frame)
                            if self.tracking_results is not None:
                                self.last_valid_tracking_results = self.tracking_results
                                self.tracking_result_valid_frames = 0
                            self.tracking_inference_count += 1

                        if self.config.is_segmentation_enabled():
                            self.segmentation_results = self._sync_segmentation_inference(frame)
                            if self.segmentation_results is not None:
                                self.last_valid_segmentation_results = self.segmentation_results
                                self.segmentation_result_valid_frames = 0
                            self.segmentation_inference_count += 1
                else:
                    # 非推理帧：复用上一个完整的融合结果
                    if self.config.is_tracking_enabled() and self.last_valid_tracking_results is not None:
                        if self.tracking_result_valid_frames < self.max_result_valid_frames:
                            self.tracking_results = self.last_valid_tracking_results
                            self.tracking_result_valid_frames += 1
                            logging.debug(f"Reusing tracking result at frame {count} (valid for {self.tracking_result_valid_frames} frames)")
                        else:
                            self.tracking_results = None
                            logging.debug(f"Tracking result expired at frame {count}")

                    if self.config.is_segmentation_enabled() and self.last_valid_segmentation_results is not None:
                        if self.segmentation_result_valid_frames < self.max_result_valid_frames:
                            self.segmentation_results = self.last_valid_segmentation_results
                            self.segmentation_result_valid_frames += 1
                            logging.debug(f"Reusing segmentation result at frame {count} (valid for {self.segmentation_result_valid_frames} frames)")
                        else:
                            self.segmentation_results = None
                            logging.debug(f"Segmentation result expired at frame {count}")

                # 确保检测结果连续性
                self._ensure_detection_continuity()
                
                # 创建融合显示帧 - 添加性能监控
                frame_start_time = time.time()
                display_frame = self._create_fused_display_frame(frame)

                # 计算当前帧目标数量
                self.current_target_count = self._count_current_targets()

                # 更新性能统计
                self.performance_stats['frame_count'] += 1
                self.performance_stats['total_targets'] += self.current_target_count
                if self.performance_stats['frame_count'] > 0:
                    self.avg_target_count = self.performance_stats['total_targets'] / self.performance_stats['frame_count']

                # 智能性能调整
                if self.config.adaptive_quality:
                    self._adaptive_performance_adjustment()

                frame_process_time = time.time() - frame_start_time
                self.performance_stats['processing_times'].append(frame_process_time)
                if len(self.performance_stats['processing_times']) > 100:
                    self.performance_stats['processing_times'].pop(0)  # 保留最近100帧的处理时间

                # 处理告警逻辑 - 统一帧级别告警管理
                self._process_frame_level_alerts(display_frame, frame, t1, count)

                # 保存原始帧
                try:
                    self.save_queue.put_nowait(frame)
                except queue.Full:
                    pass

                # 统一推流队列管理 - 确保所有推流帧都包含最新检测结果
                # 更新最后显示帧缓存
                self.last_display_frame = display_frame.copy()
                self.last_display_frame_time = time.time()

                # 将最终显示帧放入推流队列
                try:
                    # 检查队列压力，决定是否跳帧
                    queue_pressure = self.push_queue.qsize() / self.push_queue.maxsize
                    should_add_frame = True

                    if self.config.performance_mode and queue_pressure > 0.8:
                        # 性能模式下，队列压力大时智能跳帧
                        should_add_frame = (count % 2 == 0)  # 每两帧保留一帧

                    if should_add_frame:
                        self.push_queue.put_nowait(display_frame)
                except queue.Full:
                    # 如果推流队列满了，使用智能丢帧策略
                    try:
                        # 丢弃最旧的帧，保留最新的检测结果
                        for _ in range(min(2, self.push_queue.qsize())):
                            self.push_queue.get_nowait()
                        self.push_queue.put_nowait(display_frame)
                    except queue.Empty:
                        pass
                
                self.dataT = time.time() - t1
    
                if count % 200 == 199:
                    avg_processing_time = sum(self.performance_stats['processing_times'][-50:]) / min(50, len(self.performance_stats['processing_times'])) if self.performance_stats['processing_times'] else 0
                    logging.info(f"Performance Stats - Frame: {self.dataT:.4f}s, Targets: {self.current_target_count}, "
                               f"Avg Targets: {self.avg_target_count:.1f}, Avg Process: {avg_processing_time:.4f}s, "
                               f"Tracking: {self.tracking_inference_count}, Segmentation: {self.segmentation_inference_count}, "
                               f"Performance Mode: {self.config.performance_mode}")
                    
                count += 1
                
        except Exception as e:
            logging.error(f"Frame processing error: {e}")

    def _create_fused_display_frame(self, frame):
        """创建融合显示帧 - 增强结果复用，确保检测框连续显示"""
        display_frame = frame.copy()

        try:
            # 绘制追踪结果 - 优先使用当前结果，否则使用缓存结果
            tracking_results_to_draw = None
            if (self.config.is_tracking_enabled()):
                if (self.tracking_results is not None and
                    len(self.tracking_results) > 0 and
                    self.tracking_results[0].boxes is not None and
                    len(self.tracking_results[0].boxes) > 0):
                    tracking_results_to_draw = self.tracking_results
                elif (self.last_valid_tracking_results is not None and
                      self.tracking_result_valid_frames < self.max_result_valid_frames):
                    # 使用缓存的有效结果，避免检测框突然消失
                    tracking_results_to_draw = self.last_valid_tracking_results
                    logging.debug(f"Using cached tracking results (frame {self.tracking_result_valid_frames}/{self.max_result_valid_frames})")

                if tracking_results_to_draw is not None:
                    display_frame = self._draw_tracking_results(display_frame, tracking_results_to_draw)

            # 绘制分割结果 - 优先使用当前结果，否则使用缓存结果
            segmentation_results_to_draw = None
            if (self.config.is_segmentation_enabled()):
                if (self.segmentation_results is not None and
                    len(self.segmentation_results) > 0 and
                    (hasattr(self.segmentation_results[0], 'masks') and self.segmentation_results[0].masks is not None or
                     hasattr(self.segmentation_results[0], 'boxes') and self.segmentation_results[0].boxes is not None)):
                    segmentation_results_to_draw = self.segmentation_results
                elif (self.last_valid_segmentation_results is not None and
                      self.segmentation_result_valid_frames < self.max_result_valid_frames):
                    # 使用缓存的有效结果，避免分割掩码突然消失
                    segmentation_results_to_draw = self.last_valid_segmentation_results
                    logging.debug(f"Using cached segmentation results (frame {self.segmentation_result_valid_frames}/{self.max_result_valid_frames})")

                if segmentation_results_to_draw is not None:
                    display_frame = self._draw_segmentation_results(display_frame, segmentation_results_to_draw)

        except Exception as e:
            logging.error(f"Error creating fused display frame: {e}")
            display_frame = frame.copy()

        return display_frame

    def _ensure_detection_continuity(self):
        """确保检测结果的连续性，避免闪烁"""
        # 更新追踪结果有效帧计数
        if self.tracking_results is not None:
            self.last_valid_tracking_results = self.tracking_results
            self.tracking_result_valid_frames = 0
        else:
            self.tracking_result_valid_frames += 1

        # 更新分割结果有效帧计数
        if self.segmentation_results is not None:
            self.last_valid_segmentation_results = self.segmentation_results
            self.segmentation_result_valid_frames = 0
        else:
            self.segmentation_result_valid_frames += 1

        # 如果缓存结果过期，清除缓存
        if self.tracking_result_valid_frames > self.max_result_valid_frames:
            self.last_valid_tracking_results = None
            self.tracking_result_valid_frames = 0

        if self.segmentation_result_valid_frames > self.max_result_valid_frames:
            self.last_valid_segmentation_results = None
            self.segmentation_result_valid_frames = 0

    def _count_current_targets(self):
        """计算当前帧的目标数量"""
        target_count = 0
        
        # 统计追踪目标
        if (self.config.is_tracking_enabled() and 
            self.tracking_results is not None and 
            len(self.tracking_results) > 0 and 
            self.tracking_results[0].boxes is not None):
            target_count += len(self.tracking_results[0].boxes)
        
        # 统计分割目标
        if (self.config.is_segmentation_enabled() and 
            self.segmentation_results is not None and 
            len(self.segmentation_results) > 0 and 
            self.segmentation_results[0].boxes is not None):
            target_count += len(self.segmentation_results[0].boxes)
            
        return target_count
    
    def _adaptive_performance_adjustment(self):
        """自适应性能调整"""
        # 如果目标数量过多或处理时间过长，启用性能模式
        avg_processing_time = sum(self.performance_stats['processing_times'][-10:]) / min(10, len(self.performance_stats['processing_times'])) if self.performance_stats['processing_times'] else 0
        
        should_enable_performance_mode = (
            self.current_target_count > self.config.max_targets_threshold or
            avg_processing_time > 0.1  # 处理时间超过100ms
        )
        
        if should_enable_performance_mode and not self.config.performance_mode:
            logging.info(f"Enabling performance mode: targets={self.current_target_count}, avg_time={avg_processing_time:.4f}s")
            self.config.performance_mode = True
            # 动态调整推理间隔
            self.config.tracking_inference_interval = min(4, self.config.tracking_inference_interval + 1)
            self.config.segmentation_inference_interval = min(6, self.config.segmentation_inference_interval + 1)
        elif not should_enable_performance_mode and self.config.performance_mode:
            logging.info(f"Disabling performance mode: targets={self.current_target_count}, avg_time={avg_processing_time:.4f}s")
            self.config.performance_mode = False
            # 恢复正常推理间隔
            self.config.tracking_inference_interval = 2
            self.config.segmentation_inference_interval = 3
    
    def _draw_tracking_results(self, frame, results):
        """绘制追踪结果"""
        try:
            if results and len(results) > 0:
                # 使用plot方法绘制追踪结果
                frame = results[0].plot(img=frame, conf=True, line_width=2, font_size=0.5)
        except Exception as e:
            logging.error(f"Error drawing tracking results: {e}")
        return frame

    def _draw_segmentation_results(self, frame, results):
        """绘制分割结果"""
        try:
            if results and len(results) > 0:
                # 使用plot方法绘制分割结果（包含掩码）
                frame = results[0].plot(img=frame, conf=True, line_width=2, font_size=0.5, masks=True)
        except Exception as e:
            logging.error(f"Error drawing segmentation results: {e}")
        return frame

    def _process_frame_level_alerts(self, display_frame, frame, t1, count):
        """统一的帧级别告警处理 - 确保同一帧中只告警一次"""
        current_time = time.time()
        
        # 检查告警间隔是否满足
        interval_satisfied = (current_time - self.last_alarm_time) >= self.config.alarm_interval_seconds
        
        if not interval_satisfied:
            # 如果告警间隔不满足，直接返回，不处理任何告警
            return
        
        # 收集所有需要告警的目标
        tracking_targets = set()
        segmentation_targets = set()
        total_target_count = 0
        
        # 处理追踪算法的目标检测
        if self.config.is_tracking_enabled() and self.tracking_results:
            tracking_targets, tracking_count = self._collect_tracking_targets(display_frame, frame)
            total_target_count += tracking_count
        
        # 处理分割算法的目标检测
        if self.config.is_segmentation_enabled() and self.segmentation_results:
             segmentation_targets, segmentation_count = self._collect_segmentation_targets(display_frame, frame)
             total_target_count += segmentation_count
             # 保存分割目标供后续处理使用
             self.segmentation_targets = segmentation_targets
        
        # 检查是否满足告警条件：总目标数量超过阈值
        if total_target_count > self.config.alarm_target_threshold:
            # 确定告警类型和目标
            if tracking_targets and segmentation_targets:
                alarm_type = "hybrid"
                all_targets = tracking_targets.union(segmentation_targets)
            elif tracking_targets:
                alarm_type = "tracking"
                all_targets = tracking_targets
            else:
                alarm_type = "segmentation"
                all_targets = segmentation_targets
            
            logging.info(f"Frame-level alarm triggered: {total_target_count} targets detected (threshold: {self.config.alarm_target_threshold}), interval satisfied ({current_time - self.last_alarm_time:.2f}s >= {self.config.alarm_interval_seconds}s), type: {alarm_type}")
            
            # 触发帧级别告警
            self._trigger_frame_level_alarm(alarm_type, all_targets, display_frame, current_time)
            
            # 处理追踪目标的首图保存和视频剪辑
            if tracking_targets:
                self._handle_tracking_targets_post_alarm(tracking_targets, display_frame)
        else:
            logging.debug(f"Frame-level alarm not triggered: total target count {total_target_count} <= threshold {self.config.alarm_target_threshold}")
    
    def _collect_tracking_targets(self, display_frame, frame):
        """收集追踪算法的目标，返回目标集合和数量"""
        current_targets = set()
        
        # 处理追踪结果
        if (self.tracking_results is not None and 
            len(self.tracking_results) > 0 and 
            self.tracking_results[0].boxes is not None):
            
            for result in self.tracking_results:
                if result.boxes is not None:
                    for j, box in enumerate(result.boxes):
                        if box.id is not None:
                            target_id = int(box.id)
                            cls = int(box.cls)
                            current_targets.add(target_id)
                            self.target_classes[target_id] = self.tracking_classes[cls]
                            
                            if target_id not in self.target_buffers:
                                self.target_buffers[target_id] = deque(maxlen=self.buffer_maxlen)
                                self.target_buffers[target_id].append(display_frame)
        
        return current_targets, len(current_targets)
    
    def _collect_segmentation_targets(self, display_frame, frame):
        """收集分割算法的目标，返回目标集合和数量"""
        current_detections = []
        
        # 处理分割结果
        if (self.segmentation_results is not None and 
            len(self.segmentation_results) > 0):
            
            for result in self.segmentation_results:
                if result.boxes is not None:
                    for box in result.boxes:
                        cls = int(box.cls)
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        detection_id = f"{cls}_{int(x1)}_{int(y1)}_{int(x2)}_{int(y2)}"
                        current_detections.append(detection_id)
        
        return set(current_detections), len(current_detections)
    
    def _handle_tracking_targets_post_alarm(self, tracking_targets, display_frame):
        """处理追踪目标告警后的首图保存和视频剪辑任务"""
        # 处理新检测到的追踪目标
        new_targets = tracking_targets - self.alerted_targets
        if new_targets:
            for target_id in new_targets:
                self.alerted_targets.add(target_id)
                cls = int([box.cls for result in self.tracking_results for j, box in enumerate(result.boxes) if box.id is not None and int(box.id) == target_id][0])
                
                # 保存首图
                alert_dir = os.path.join(self.result_path, self.video_name)
                os.makedirs(alert_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d-%H%M%S")
                alert_image_path = os.path.join(
                    alert_dir, 
                    f"target_{target_id}_{self.tracking_classes[cls]}_{self.video_name}_{timestamp}.jpg"
                )
                cv2.imwrite(alert_image_path, display_frame)
                self.target_first_image_paths[target_id] = alert_image_path
                logging.info(f"First image saved for target {target_id}: {alert_image_path}")

                # 启动视频剪辑任务
                if target_id not in self.clip_jobs:
                    self.clip_jobs[target_id] = {
                        "frames": [],  # 使用列表而不是deque，避免因maxlen丢帧
                        "deadline": time.time() + self.clip_seconds,
                        "class": self.tracking_classes[cls],
                        "max_frames": self.clip_seconds * self.clip_fps  # 记录最大帧数用于后续处理
                    }
        
        # 处理新检测到的分割目标
        if hasattr(self, 'segmentation_targets') and self.segmentation_targets:
            new_segmentation_detections = [detection_id for detection_id in self.segmentation_targets if detection_id not in self.segmentation_alerted_targets]
            for detection_id in new_segmentation_detections:
                self.segmentation_alerted_targets.add(detection_id)
    
    def _process_tracking_alerts_legacy(self, display_frame, frame, t1, count):
        """处理追踪算法的告警"""
        current_targets = set()
        
        # 处理检测到的目标
        for result in self.tracking_results:
            if result.boxes is not None and hasattr(result.boxes, 'id') and result.boxes.id is not None:
                for j, box in enumerate(result.boxes):
                    if box.id is not None:
                        target_id = int(box.id)
                        cls = int(box.cls)
                        current_targets.add(target_id)
                        self.target_classes[target_id] = self.tracking_classes[cls]
                        
                        if target_id not in self.target_buffers:
                            self.target_buffers[target_id] = deque(maxlen=self.buffer_maxlen)
                            self.target_buffers[target_id].append(display_frame)

        # 检查是否满足告警条件：目标数量超过阈值 AND 报警间隔满足
        current_time = time.time()
        target_count_exceeded = len(current_targets) > self.config.alarm_target_threshold
        interval_satisfied = (current_time - self.last_alarm_time) >= self.config.alarm_interval_seconds
        
        # 只有当两个条件都满足时才处理告警
        if target_count_exceeded and interval_satisfied:
            # 处理新检测到的目标
            new_targets = current_targets - self.alerted_targets
            if new_targets:
                logging.info(f"Tracking alarm triggered: {len(current_targets)} targets detected (threshold: {self.config.alarm_target_threshold}), interval satisfied ({current_time - self.last_alarm_time:.2f}s >= {self.config.alarm_interval_seconds}s)")
                
                # 按帧级别触发告警：同一帧中只告警一次
                self._trigger_frame_level_alarm("tracking", current_targets, display_frame, current_time)
                
                # 为新目标保存首图和启动视频剪辑任务
                for target_id in new_targets:
                    self.alerted_targets.add(target_id)
                    cls = int([box.cls for result in self.tracking_results for j, box in enumerate(result.boxes) if box.id is not None and int(box.id) == target_id][0])
                    
                    # 保存首图
                    alert_dir = os.path.join(self.result_path, self.video_name)
                    os.makedirs(alert_dir, exist_ok=True)
                    timestamp = time.strftime("%Y%m%d-%H%M%S")
                    alert_image_path = os.path.join(
                        alert_dir, 
                        f"target_{target_id}_{self.tracking_classes[cls]}_{self.video_name}_{timestamp}.jpg"
                    )
                    cv2.imwrite(alert_image_path, display_frame)
                    self.target_first_image_paths[target_id] = alert_image_path
                    logging.info(f"First image saved for target {target_id}: {alert_image_path}")

                    # 启动视频剪辑任务
                    if target_id not in self.clip_jobs:
                        self.clip_jobs[target_id] = {
                            "frames": [],  # 使用列表而不是deque，避免因maxlen丢帧
                            "deadline": time.time() + self.clip_seconds,
                            "class": self.tracking_classes[cls],
                            "max_frames": self.clip_seconds * self.clip_fps  # 记录最大帧数用于后续处理
                        }
        else:
            # 记录未满足告警条件的情况
            if not target_count_exceeded:
                logging.debug(f"Tracking alarm not triggered: target count {len(current_targets)} <= threshold {self.config.alarm_target_threshold}")
            if not interval_satisfied:
                logging.debug(f"Tracking alarm not triggered: interval not satisfied ({current_time - self.last_alarm_time:.2f}s < {self.config.alarm_interval_seconds}s)")
        # 将当前带框帧灌入所有进行中的4s任务，并到期落盘+上报
        self._check_and_finalize_clips(display_frame)
         
        # 清理“消失目标”的缓存（不触发保存）
        with self.alert_lock:
            disappeared_targets = self.previous_targets - current_targets
            for target_id in disappeared_targets:
                self.target_buffers.pop(target_id, None)
                self.target_classes.pop(target_id, None)
                self.target_first_frames.pop(target_id, None)
            self.previous_targets = current_targets.copy()
            self.active_targets = current_targets.copy()

        # 保存主视频（原始帧）
        try:
            self.save_queue.put_nowait(frame)
        except queue.Full:
            pass
        # 推流
        try:
            self.push_queue.put_nowait(display_frame)
        except queue.Full:
            pass

        self.dataT = time.time() - t1
        if count % 200 == 199:
            logging.info(f"Tracking inference time: {self.dataT:.4f}s")

    def _process_segmentation_alerts_legacy(self, display_frame, frame):
        """处理分割算法的告警 - 已弃用，由统一的帧级别告警处理替代"""
        current_detections = []
        
        # 处理检测到的目标
        for result in self.segmentation_results:
            if result.boxes is not None:
                for j, box in enumerate(result.boxes):
                    cls = int(box.cls)
                    conf = float(box.conf)
                    
                    # 边界检查
                    if cls >= len(self.config.segmentation_classes):
                        logging.warning(f"Class index {cls} out of range for segmentation_classes (max: {len(self.config.segmentation_classes)-1}), using 'unknown'")
                        class_name = "unknown"
                    else:
                        class_name = self.config.segmentation_classes[cls]
                    
                    # 为每个检测生成唯一标识（基于位置和类别）
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    detection_id = f"{cls}_{int(x1)}_{int(y1)}_{int(x2)}_{int(y2)}"
                    current_detections.append(detection_id)
                    
                    # 检测到目标时记录，但不立即触发告警
                    # 告警将在后续根据目标数量阈值统一处理
        
        # 检查是否满足告警条件：分割目标数量超过阈值 AND 报警间隔满足
        current_time = time.time()
        target_count_exceeded = len(current_detections) > self.config.alarm_target_threshold
        interval_satisfied = (current_time - self.last_alarm_time) >= self.config.alarm_interval_seconds
        
        if target_count_exceeded and interval_satisfied:
            # 处理新检测到的目标
            new_detections = [detection_id for detection_id in current_detections if detection_id not in self.segmentation_alerted_targets]
            if new_detections:
                logging.info(f"Segmentation alarm triggered: {len(current_detections)} targets detected (threshold: {self.config.alarm_target_threshold}), interval satisfied ({current_time - self.last_alarm_time:.2f}s >= {self.config.alarm_interval_seconds}s)")
                
                # 按帧级别触发告警：同一帧中只告警一次
                self._trigger_frame_level_alarm("segmentation", set(current_detections), display_frame, current_time)
                
                # 标记新检测为已告警
                for detection_id in new_detections:
                    self.segmentation_alerted_targets.add(detection_id)
        else:
            # 记录未满足告警条件的情况
            if not target_count_exceeded:
                logging.debug(f"Segmentation alarm not triggered: target count {len(current_detections)} <= threshold {self.config.alarm_target_threshold}")
            if not interval_satisfied:
                logging.debug(f"Segmentation alarm not triggered: interval not satisfied ({current_time - self.last_alarm_time:.2f}s < {self.config.alarm_interval_seconds}s)")
        
        # 将带分割掩码的帧添加到缓存中
        if not hasattr(self, 'frame_buffer'):
            self.frame_buffer = deque(maxlen=150)
        self.frame_buffer.append(display_frame)

    def _check_and_finalize_clips(self, display_frame_or_none):
            now = time.time()
            to_finalize = []
            # 投喂帧
            if display_frame_or_none is not None:
                for tid, job in list(self.clip_jobs.items()):
                    job["frames"].append(display_frame_or_none)
                    # 可选：限制内存使用，保留最近的帧（但保留足够的帧数）
                    max_frames = job.get("max_frames", self.clip_seconds * self.clip_fps)
                    if len(job["frames"]) > max_frames * 1.5:  # 允许一定的缓冲
                        job["frames"] = job["frames"][-max_frames:]
            # 到期落盘并上报
            for tid, job in list(self.clip_jobs.items()):
                if now >= job["deadline"]:
                    frames_for_save = list(job["frames"])
                    target_class = job["class"]
                    t = threading.Thread(
                        target=self.save_detection_clip,
                        args=(tid, target_class, frames_for_save),
                    )
                    t.start()
                    self.writer_threads.append(t)
                    to_finalize.append(tid)
            for tid in to_finalize:
                self.clip_jobs.pop(tid, None)
    # 生成 4s 片段并一次性上传 + 上报；失败则回退只上报图片
    def save_detection_clip(self, target_id, target_class, frames):
        if not frames:
            logging.warning(f"No frames for clip of target {target_id}")
            self._report_image_only_fallback(target_id, target_class)
            return

        # 控制帧数，确保视频时长准确
        target_frames = self.clip_seconds * self.clip_fps
        
        # 记录原始帧数
        original_frame_count = len(frames)
        
        # 如果帧数超过目标帧数，保留最后的目标帧数
        if len(frames) > target_frames:
            frames = frames[-target_frames:]
            logging.info(f"Trimmed frames for target {target_id}: {original_frame_count} -> {len(frames)}")
        
        # 低于1秒补帧，避免0:00
        MIN_FRAMES = self.clip_fps
        if len(frames) < MIN_FRAMES:
            before_padding = len(frames)
            frames = frames + [frames[-1]] * (MIN_FRAMES - len(frames))
            logging.info(f"Padded frames for target {target_id}: {before_padding} -> {len(frames)}")

        alert_video_dir = os.path.join(self.result_path, self.video_name)
        os.makedirs(alert_video_dir, exist_ok=True)
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        clip_path = os.path.join(
            alert_video_dir,
            f"target_{target_id}_{target_class}_{self.video_name}_{timestamp}_appear4s.mp4"
        )

        # 记录最终帧数和预期视频时长
        final_frame_count = len(frames)
        expected_duration = final_frame_count / self.clip_fps
        logging.info(f"Saving video for target {target_id}: {final_frame_count} frames, expected duration: {expected_duration:.2f}s")

        ok = self.write_clip_video(frames, clip_path)
        if not ok or (not os.path.exists(clip_path) or os.path.getsize(clip_path) == 0):
            logging.error(f"Clip write failed for target {target_id}, fallback to image-only alarm.")
            self._report_image_only_fallback(target_id, target_class)
            return

        logging.info(f"Saved 4s detection clip: {clip_path}")

        # 一次性上传图片+视频并上报
        try:
            alert_image_path = self.target_first_image_paths.get(target_id, "")
            self.call_minio_and_alarm(
                alert_image_path=alert_image_path if alert_image_path and os.path.exists(alert_image_path) else "",
                alert_video_path=clip_path,
                alarm_level="1",
                alarmClass=target_class
            )
        except Exception as e:
            logging.error(f"call_minio_and_alarm failed: {e}")
            # 如果视频上报失败，尝试只上报图片（若有）
            self._report_image_only_fallback(target_id, target_class)

    # def _trigger_tracking_alert(self, target_id, class_name, display_frame):
    #     """触发追踪算法告警"""
    #     try:
    #         # 保存第一次出现时的带检测框的帧
    #         self.target_first_frames[target_id] = display_frame.copy()
            
    #         # 标记该目标已告警
    #         self.alerted_targets.add(target_id)
            
    #         # 立即保存告警图片
    #         alert_video_dir = os.path.join(self.result_path, self.video_name)
    #         if not os.path.exists(alert_video_dir):
    #             os.makedirs(alert_video_dir)
            
    #         res_backup_dir = os.path.join(self.result_path, "res", self.video_name)
    #         if not os.path.exists(res_backup_dir):
    #             os.makedirs(res_backup_dir)
            
    #         timestamp = time.strftime("%Y%m%d-%H%M%S")
    #         alert_image_path = os.path.join(
    #             alert_video_dir, 
    #             f"tracking_target_{target_id}_{class_name}_{self.video_name}_{timestamp}.jpg"
    #         )
            
    #         res_image_path = os.path.join(
    #             res_backup_dir, 
    #             f"tracking_target_{target_id}_{class_name}_{self.video_name}_{timestamp}.jpg"
    #         )
            
    #         # 保存告警图片到两个位置
    #         cv2.imwrite(alert_image_path, display_frame)
    #         cv2.imwrite(res_image_path, display_frame)
            
    #         # 异步上传图片并发送告警
    #         threading.Thread(
    #             target=self._call_minio_and_alarm_immediate,
    #             args=(alert_image_path, f"tracking_{target_id}", class_name),
    #             daemon=True
    #         ).start()
            
    #         logging.info(f"Tracking target {target_id} ({class_name}) first detected - IMMEDIATE ALERT triggered")
            
    #     except Exception as e:
    #         logging.error(f"Error triggering tracking alert for target {target_id}: {e}")

    def _trigger_frame_level_alarm(self, alarm_type, targets, display_frame, current_time):
        """按帧级别触发告警 - 同一帧中只告警一次"""
        try:
            # 更新最后告警时间
            self.last_alarm_time = current_time
            
            # 保存告警图片
            alert_video_dir = os.path.join(self.result_path, self.video_name)
            if not os.path.exists(alert_video_dir):
                os.makedirs(alert_video_dir)
            
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            alert_image_path = os.path.join(
                alert_video_dir, 
                f"frame_alarm_{alarm_type}_{len(targets)}targets_{self.video_name}_{timestamp}.jpg"
            )
            
            # 保存告警图片
            cv2.imwrite(alert_image_path, display_frame)
            
            # 确定告警类别
            if alarm_type == "tracking":
                # 获取追踪目标的主要类别
                target_classes = []
                for target_id in targets:
                    if target_id in self.target_classes:
                        target_classes.append(self.target_classes[target_id])
                alarm_class = target_classes[0] if target_classes else "unknown"
            else:  # segmentation
                # 获取分割检测的主要类别
                alarm_class = "mixed_targets"  # 分割检测可能包含多种类别
            
            # 异步上传图片并发送告警
            threading.Thread(
                target=self._call_minio_and_alarm_immediate,
                args=(alert_image_path, f"frame_{alarm_type}_{len(targets)}", alarm_class),
                daemon=True
            ).start()
            
            logging.info(f"Frame-level {alarm_type} alarm triggered: {len(targets)} targets detected - single alarm sent for entire frame")
            
        except Exception as e:
            logging.error(f"Error triggering frame-level {alarm_type} alarm: {e}")

    def _trigger_segmentation_alert(self, detection_id, class_name, display_frame):
        """触发分割算法告警 - 已弃用，改用帧级别告警"""
        # 此方法已被_trigger_frame_level_alarm替代，保留以防兼容性问题
        logging.warning(f"_trigger_segmentation_alert is deprecated, using frame-level alarm instead")
        pass

    # def _save_target_video_only(self, target_id, target_class, alert_frames):
    #     """仅保存视频，不触发告警（用于目标消失时）"""
    #     if not alert_frames:
    #         return
        
    #     try:
    #         # 创建以 video_name 命名的文件夹
    #         alert_video_dir = os.path.join(self.result_path, self.video_name)
            
    #         if not os.path.exists(alert_video_dir):
    #             os.makedirs(alert_video_dir)
            
    #         res_backup_dir = os.path.join(self.result_path, "res", self.video_name)
    #         if not os.path.exists(res_backup_dir):
    #             os.makedirs(res_backup_dir)
            
    #         # 生成时间戳
    #         timestamp = time.strftime("%Y%m%d-%H%M%S")
            
    #         # 告警视频路径
    #         alert_video_path = os.path.join(
    #             alert_video_dir, 
    #             f"tracking_target_{target_id}_{target_class}_{self.video_name}_{timestamp}_video_only.mp4"
    #         )
            
    #         res_video_path = os.path.join(
    #             res_backup_dir, 
    #             f"tracking_target_{target_id}_{target_class}_{self.video_name}_{timestamp}_video_only.mp4"
    #         )
            
    #         # 仅保存视频，不上传不告警
    #         threading.Thread(
    #             target=self._write_target_video_only, 
    #             args=(alert_frames, alert_video_path, res_video_path, target_id, target_class),
    #             daemon=True
    #         ).start()
            
    #         logging.info(f"Saving video only for target {target_id} ({target_class}): {alert_video_path}")
            
    #     except Exception as e:
    #         logging.error(f"Error saving target video for {target_id}: {e}")
    def _report_image_only_fallback(self, target_id, target_class):
        """兜底：只上传图片并上报告警（当视频生成/上传失败时）"""
        img_path = self.target_first_image_paths.get(target_id, "")
        if not img_path or not os.path.exists(img_path):
            logging.error(f"No first image for fallback alarm of target {target_id}")
            return
        try:
            logging.info(f"Fallback: upload image only for target {target_id}")
            img_obj = minio_update.minio_interface(
                self.dict_json, "alarm", os.path.basename(img_path), img_path
            )
            self.drone_state = drone_state_get(self.dict_json)
            drones_server.alarm_info_post(
                active_drone=self.dict_json,
                drone_state=self.drone_state,
                classes="gaosu",
                alarmLevel="1",
                alarmImageUrl=img_obj,
                videoUrl="placeholder_video.mp4"  # 修复：提供占位符视频URL
            )
            logging.info("Fallback image-only alarm posted.")
        except Exception as e:
            logging.error(f"Fallback image-only alarm failed: {e}")

    def write_clip_video(self, frames, out_path):
        """写入4秒检测片段视频"""
        command = [
            'ffmpeg', '-y',
            '-f', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '1920x1280',
            '-framerate', str(self.clip_fps),
            '-i', 'pipe:0',
            '-c:v', 'h264_nvenc',   # 如不稳定换 libx264
            '-pix_fmt', 'yuv420p',
            '-crf', '20',
            '-movflags', '+faststart',
            out_path
        ]
        try:
            pipe = subprocess.Popen(command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
            for frame in frames:
                frame = np.ascontiguousarray(frame, dtype=np.uint8)
                pipe.stdin.write(frame.tobytes())
            pipe.stdin.close()
            ret = pipe.wait()
            err = pipe.stderr.read().decode('utf-8', errors='ignore')
            if ret != 0:
                logging.error(f"FFmpeg failed ({ret}) when writing {out_path}: {err}")
                return False
            logging.info(f"FFmpeg OK: {out_path}")
            return True
        except Exception as e:
            logging.error(f"Failed to write clip {out_path}: {e}")
            return False

    def _write_target_video_only(self, frames, alert_video_path, res_video_path, target_id, target_class):
        """写入目标视频文件"""
        for video_path in [alert_video_path, res_video_path]:
            command = [
                'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
                '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',
                '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-crf', '20', video_path
            ]
            try:
                pipe = subprocess.Popen(command, stdin=subprocess.PIPE, bufsize=0)
                for frame in frames:
                    pipe.stdin.write(frame.tobytes())
                pipe.stdin.close()
                pipe.wait()
                logging.info(f"Target video saved: {video_path}")
            except Exception as e:
                logging.error(f"Failed to save target video {video_path}: {e}")

    # def _call_minio_and_alarm_immediate(self, alert_image_path, alert_id, target_class):
    #     """立即告警 - 仅上传图片并发送告警信息"""
    #     try:
    #         # 调用 minio_update 上传图片
    #         logging.info(f"Uploading image to MinIO: {alert_image_path}")
    #         return_image_name = minio_update.minio_interface(
    #             self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
    #         )
    #         logging.info(f"Image uploaded to MinIO: {return_image_name}")
            
    #         # 调用 alarm_info_post 发送告警
    #         logging.info(f"Posting immediate alarm info to server")
            
    #         self.drone_state = drone_state_get(self.dict_json)

    #         drones_server.alarm_info_post(
    #             active_drone=self.dict_json,
    #             drone_state=self.drone_state,
    #             classes="gaosu",
    #             alarmLevel="1",
    #             alarmImageUrl=return_image_name,
    #             videoUrl="placeholder_video.mp4"  # 修复：提供占位符视频URL
    #         )
    #         logging.info(f"Immediate alarm posted for {alert_id} with class {target_class}")
    #     except Exception as e:
    #         logging.error(f"Failed to send immediate alarm for {alert_id}: {e}")
    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):
            """
            一次性：上传图片(可选)和视频到 MinIO，并发送告警信息到服务器。
            图片桶: alarm；视频桶: clip
            """
            return_image_name = ""
            return_video_name = ""

            # 图
            if alert_image_path:
                try:
                    logging.info(f"Uploading image to MinIO: {alert_image_path}")
                    return_image_name = minio_update.minio_interface(
                        self.dict_json, "alarm",
                        os.path.basename(alert_image_path), alert_image_path
                    )
                    logging.info(f"Image uploaded: {return_image_name}")
                except Exception as e:
                    logging.error(f"Upload image failed: {e}")

            # 视
            try:
                logging.info(f"Uploading video to MinIO: {alert_video_path}")
                return_video_name = minio_update.minio_interface(
                    self.dict_json, "clip",
                    os.path.basename(alert_video_path), alert_video_path
                )
                logging.info(f"Video uploaded: {return_video_name}")
            except Exception as e:
                logging.error(f"Upload video failed: {e}")
                # 不中断：仍尝试上报（可能仅图片）

            # 上报（图片可为空字符串）
            try:
                logging.info("Posting alarm info (image+video, once)")
                self.drone_state = drone_state_get(self.dict_json)
                drones_server.alarm_info_post(
                    active_drone=self.dict_json,
                    drone_state=self.drone_state,
                    classes="gaosu",
                    alarmLevel=alarm_level,
                    alarmImageUrl=return_image_name,
                    videoUrl=return_video_name
                )
                logging.info("Alarm info posted (combined).")
            except Exception as e:
                logging.error(f"Post alarm failed: {e}")
                # 若失败，可考虑重试/入队，这里先记录日志
    def pushImg(self):
        """优化的推流图像方法 - 确保检测结果连续显示，避免闪烁"""
        try:
            frame_count = 0
            last_log_time = time.time()
            last_frame_time = time.time()
            target_fps = 25  # 与FFmpeg输出帧率保持一致
            frame_interval = 1.0 / target_fps

            while self.is_switchOn:
                current_time = time.time()

                # 精确的帧率控制
                time_since_last = current_time - last_frame_time
                if time_since_last < frame_interval:
                    remaining_time = frame_interval - time_since_last
                    if remaining_time > 0.001:
                        time.sleep(remaining_time)
                    continue

                # 统一的帧获取策略 - 确保连续性
                frame = None
                frame_source = "none"

                try:
                    # 策略1：从推流队列获取最新的显示帧
                    if not self.push_queue.empty():
                        # 获取队列中最新的帧，丢弃过期帧
                        if not self.push_queue.empty():
                            frame = self.push_queue.get_nowait()
                        frame_source = "push_queue"

                    # 策略2：如果队列为空，使用缓存的最后显示帧
                    elif (self.last_display_frame is not None and
                          current_time - self.last_display_frame_time < self.display_frame_timeout):
                        frame = self.last_display_frame.copy()
                        frame_source = "cached_display_frame"

                    # 策略3：如果缓存帧也过期，等待新帧
                    else:
                        time.sleep(0.001)
                        continue

                except queue.Empty:
                    # 如果队列为空，使用缓存帧
                    if (self.last_display_frame is not None and
                        current_time - self.last_display_frame_time < self.display_frame_timeout):
                        frame = self.last_display_frame.copy()
                        frame_source = "fallback_cached_frame"
                    else:
                        time.sleep(0.001)
                        continue
                
                # 推流帧处理
                if frame is not None:
                    try:
                        # 确保帧尺寸正确
                        if frame.shape[:2] != (1280, 1920):
                            frame = cv2.resize(frame, (1920, 1280))

                        self.pipe.stdin.write(frame.tobytes())
                        last_frame_time = current_time
                        frame_count += 1

                    except Exception as e:
                        logging.error(f"Error writing frame to pipe: {e}")
                        break
                else:
                    # 如果没有可用帧，短暂等待
                    time.sleep(0.001)

                # 每10秒打印一次统计信息 - 监控推流状态
                if current_time - last_log_time >= 10:
                    actual_fps = frame_count / (current_time - last_log_time)
                    push_queue_size = self.push_queue.qsize()

                    # 计算队列压力
                    push_pressure = push_queue_size / self.push_queue.maxsize * 100

                    # 检查缓存帧状态
                    cache_status = "valid" if (self.last_display_frame is not None and
                                             current_time - self.last_display_frame_time < self.display_frame_timeout) else "expired"

                    logging.info(f"Push Stats - FPS: {actual_fps:.2f}, Targets: {getattr(self, 'current_target_count', 0)}, "
                               f"Push Queue: {push_queue_size}/{self.push_queue.maxsize} ({push_pressure:.1f}%), "
                               f"Source: {frame_source}, Cache: {cache_status}, "
                               f"Performance Mode: {getattr(self.config, 'performance_mode', False)}")

                    frame_count = 0
                    last_log_time = current_time
                    
        except Exception as e:
            logging.error(f"Push image error: {e}")
        finally:
            if hasattr(self, 'pipe') and self.pipe:
                try:
                    self.pipe.stdin.close()
                except:
                    pass

    def saveImg(self):
        try:
            while self.is_switchOn or not self.save_queue.empty():
                if not self.save_queue.empty():
                    frame = self.save_queue.get()
                    try:
                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                    except Exception as e:
                        logging.error(f"Failed to write frame to save video: {e}")
                        break
                else:
                    time.sleep(0.01)

            # 主视频上传（保留）
            if os.path.exists(self.save_full_video_path):
                return_video_name = minio_update.minio_interface(
                    self.dict_json, 
                    "full", 
                    os.path.basename(self.save_full_video_path), 
                    self.save_full_video_path
                )
                logging.info(f"Full video uploaded: {return_video_name}")
            else:
                logging.error(f"Full video not found: {self.save_full_video_path}")

        except Exception as e:
            logging.error(f"Video save error: {e}")
        finally:
            try:
                self.ffmpegSaveVideo.stdin.close()
                self.ffmpegSaveVideo.wait()
            except:
                pass
            logging.info("Video save process completed.")


class HybridStreamManager:
    """混合算法流管理器"""
    def __init__(self, config: HybridConfig):
        self.config = config
        self.processor = HybridVideoStreamProcessor(config)
        self.active_processes = {}
        self.active_streams = {}  # 用于记录正在运行的流地址
        self.streams = []
        self.lock = threading.Lock()

    def add_stream(self, dict_json, out_res):
        """添加流"""
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        with self.lock:
            if input_path in self.active_processes:
                logging.warning(f"Stream {input_path} is already running.")
                return
            
            # 创建以 video_name 命名的文件夹
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            # 设置保存路径
            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            # 启动新流的处理进程
            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path)
            )
            process.start()
            
            # 存储进程和流地址
            self.active_processes[input_path] = process
            self.active_streams[input_path] = True
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Hybrid stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        """停止流"""
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                self.processor.stop_stream(input_url)
                process.join()
                del self.active_processes[input_url]
                if input_url in self.active_streams:
                    del self.active_streams[input_url]
                logging.info(f"Stream {input_url} has been stopped.")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        """获取流列表"""
        with self.lock:
            return self.streams
    
    def is_stream_active(self, output_url):
        """检查流是否活跃"""
        with self.lock:
            return output_url in self.active_streams


class HybridVideoStreamProcessor:
    """混合算法视频流处理器"""
    
    def __init__(self, config: HybridConfig):
        self.config = config
        self.active_detectors = {}
        self.lock = multiprocessing.Lock()

    def process_stream(self, dict_json, save_path):
        """处理流"""
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        detector = HybridMultiDealImg(self.config, save_path, dict_json)
        detector.set_switch_on(True)
        detector.set_video_name(video_name)
        
        with self.lock:
            self.active_detectors[input_path] = detector
        try:
            detector.startThread(input_path, output_url, save_path)
        finally:
            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]
            logging.info(f"Hybrid stream {input_path} has finished and been removed.")

    def stop_stream(self, input_path):
        """停止流"""
        if input_path in self.active_detectors:
            detector = self.active_detectors[input_path]
            detector.set_switch_on(False)
            del self.active_detectors[input_path]
            logging.info(f"Hybrid detector for stream {input_path} has been stopped.")
        else:
            logging.warning(f"Hybrid detector for stream {input_path} not found.")
    
    def is_detector_active(self, input_path):
        """检查检测器是否活跃"""
        with self.lock:
            return input_path in self.active_detectors


# 定义比较函数，基于指定的关键字 
def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)


if __name__ == "__main__":
    setup_logging()
    
    # 创建混合算法配置
    config = HybridConfig()
    
    # 配置算法模式（可以根据需要修改）
    # config.algorithm_mode = AlgorithmMode.TRACKING_ONLY  # 仅追踪
    # config.algorithm_mode = AlgorithmMode.SEGMENTATION_ONLY  # 仅分割
    config.algorithm_mode = AlgorithmMode.HYBRID  # 混合模式（默认）
    
    # 配置模型路径
    config.tracking_model_path = "/home/<USER>/suanfa/czy/bucket.pt"
    config.segmentation_model_path = "/home/<USER>/suanfa/czy/water-0820-v1.pt"
    
    # 推理间隔配置
    config.tracking_inference_interval = 5  # 每2帧推理一次追踪
    config.segmentation_inference_interval = 5  # 每3帧推理一次分割
    
    # 启用异步推理
    config.enable_async_inference = True
    config.max_inference_threads = 2
    
    out_res = "./res"
    stream_manager = HybridStreamManager(config)
    
    # 定义关键字列表
    keys = ["droneStreamUrl"]
    
    # active_drones_list = [
    #     {
    #         "droneDeviceSn": "1581F6Q8X24BJ00G011E",
    #         "area": "芜湖轨道机巢",
    #         "monitorEq": "芜湖轨道机巢无人机",
    #         "aiVendorInfo": {
    #             "id": "1",
    #             "aiAlgorithmTypes": "[]"
    #         },
    #         "droneFlightMode": "30",
    #         "droneStreamUrl": "rtmp://10.254.152.126:1935/live2/stream2",
    #         "rootName": "中山北路",
    #         "gatewayDeviceSn": "7CTXMA600B02VF",
    #         "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
    #         "taskId": "hybrid_fusion_test",
    #         "aiStreamUrl": "rtmp://10.254.152.126:1935/live3/stream3",
    #         "timestamp": "1750069019242"
    #     }
    # ]
    
    presentStream = []
    while True:
        # 获取正在飞行任务中的无人机列表
        # active_drones_list = active_drones_get()
        #获取token
        login_token_get()
        #获取正在飞行任务中的无人机列表
        active_drones_list = active_drones_get()
        # 获取 active_drones_list 中有且 presentStream 中没有的元素
        drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
        
        if(len(drones_unique) > 0):
            for active_drone in drones_unique:        
                stream_manager.add_stream(active_drone, out_res)
        else:
            print("active_processes = ", presentStream)
            stream_manager.active_processes = {}
        presentStream = active_drones_list
        
        time.sleep(15)

### 1. 推流策略优化
# - 原始帧优先策略 ：优先推流原始帧，确保视频流畅性和一致性
# - 智能检测叠加 ：可配置的检测结果叠加比例（默认30%），避免检测结果过度干扰视频流
# - 动态策略调整 ：根据队列状态自动调整推流策略，防止检测结果积压
# ### 2. 队列管理优化
# - 增大原始帧队列 ： new_queue 从3增加到8，确保原始帧充足供应
# - 减小检测队列 ： push_queue 从5减少到3，避免检测结果积压
# - 智能队列清理 ：队列满时自动移除最旧帧，保持实时性
# ### 3. 帧率控制优化
# - 精确帧率控制 ：改进时间控制逻辑，确保稳定的30fps输出
# - 帧尺寸检查 ：自动检查和调整帧尺寸，确保推流兼容性
# - 异常处理增强 ：更好的错误处理和恢复机制
# ### 4. 配置化管理
# 在 HybridConfig 类中新增了推流优化配置：
# - prioritize_original_stream ：是否优先推流原始帧
# - detection_overlay_ratio ：检测结果叠加比例（0.0-1.0）
# - stream_quality_priority ：是否优先保证推流质量
# ### 5. 处理流程优化
# - 分离原始帧和检测帧 ：在 process_frame 中分别管理原始帧和检测结果帧
# - 条件性检测显示 ：仅在有检测结果时才将融合帧放入检测队列
# - 实时监控 ：增强的日志输出，实时监控推流状态和策略