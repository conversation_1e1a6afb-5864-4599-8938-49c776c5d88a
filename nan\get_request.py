import json
import requests
import logging

# 获取本模块的日志记录器
logger = logging.getLogger(__name__)

def get_requests_response(url,data,headers,params):

    try:
        # 发送POST请求
        response = requests.get(url,json=data, headers=headers,params=params)
        
        # 检查HTTP状态码
        if response.status_code != 200:
            logger.error(f"HTTP请求失败，状态码：{response.status_code}")
            return {}
     
        # 解析JSON响应
        json_data = response.json()
        
        # 检查code字段
        if json_data.get("code") == 200:
            return json_data
        else:
            logger.error(f"业务逻辑错误：{json_data}")
            
            return {}
            
    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求异常：{str(e)}")
    except ValueError:
        logger.error("响应内容不是有效的JSON格式")
    return {}        

if __name__ == "__main__":
    import nan.constants
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTAzMzkyMzUsInVzZXJuYW1lIjoiYWRtaW4ifQ.5ifC5TGSJYIFLrlkANEzcOcGutTh7o8gTL8vBYHuxHA"
    token = "Bearer "+token
    url = nan.constants.baseURL + nan.constants.stateAddr
    data = {}
    headers = {"Authorization": token}
    sn = "1581F6Q8X24BJ00G011E"
    params = {"sn":sn}
    
    
    result = get_requests_response(url,data,headers,params)
    print(f"获取到的result：{result}")

