import cv2
import numpy as np
from ultralytics import YOLO
import time
import logging
import json
import threading
import queue
from pathlib import Path
from collections import deque
import subprocess

class FFMpegPusher(threading.Thread):
    def __init__(self, rtmp_url, width, height, fps):
        super().__init__(daemon=True)
        self.queue = queue.Queue(maxsize=30)
        self.rtmp_url = rtmp_url
        self.width = width
        self.height = height
        self.fps = fps
        self.process = None
        self.running = True
        self.last_restart_time = 0

    def run(self):
        while self.running:
            try:
                frame = self.queue.get(timeout=1)
                if frame is None:
                    break
                if self.process is None or self.process.poll() is not None:
                    now = time.time()
                    if now - self.last_restart_time < 5:
                        continue
                    self.last_restart_time = now
                    self.start_ffmpeg()

                if not frame.flags['C_CONTIGUOUS']:
                    frame = np.ascontiguousarray(frame)
                self.process.stdin.write(frame.tobytes())
            except queue.Empty:
                continue
            except Exception as e:
                logging.warning(f"[推流线程异常]：{e}")
                self.stop_ffmpeg()

    def push(self, frame):
        if not self.queue.full():
            self.queue.put_nowait(frame)

    def start_ffmpeg(self):
        self.stop_ffmpeg()
        cmd = [
            'ffmpeg', '-y',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', f'{self.width}x{self.height}',
            '-r', str(self.fps),
            '-i', '-',
            '-c:v', 'h264_nvenc',           # ✅ 使用 GPU 编码器
            '-preset', 'p1',                # ✅ 最快编码
            '-rc', 'vbr',                   # ✅ 可变码率
            '-b:v', '4M',
            '-maxrate', '4M',
            '-bufsize', '8M',
            '-pix_fmt', 'yuv420p',
            '-g', str(int(self.fps * 2)),
            '-f', 'flv',
            self.rtmp_url
        ]
        try:
           self.process = subprocess.Popen(cmd, stdin=subprocess.PIPE)
           logging.info("✅ FFmpeg GPU 推流进程启动成功")
        except Exception as e:
           logging.error(f"❌ FFmpeg GPU 启动失败: {e}")
           self.process = None

    def stop_ffmpeg(self):
        if self.process:
            try:
                self.process.stdin.close()
                self.process.wait(timeout=2)
                logging.info("FFmpeg 推流进程已关闭")
            except Exception as e:
                logging.warning(f"关闭 FFmpeg 出错: {e}")
            self.process = None

    def stop(self):
        self.running = False
        self.queue.put(None)
        self.join()
        self.stop_ffmpeg()

def handle_alarm(alarm_id, track_id, class_name, frame_count, frame_buffer,
                 alarm_videos_dir, alarm_frames_dir, fps, width, height):
    video_filename = f"alarm_{alarm_id}_track{track_id}.mp4"
    video_path = alarm_videos_dir / video_filename

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(video_path), fourcc, fps, (width, height))

    for frame in frame_buffer:
        out.write(frame)
    out.release()

    keyframe_path = alarm_frames_dir / f"alarm_{alarm_id}_keyframe.jpg"
    if frame_buffer:
        middle_frame = list(frame_buffer)[len(frame_buffer) // 2]
        cv2.imwrite(str(keyframe_path), middle_frame)

    return {
        "alarm_id": alarm_id,
        "track_id": track_id,
        "class_name": class_name,
        "frame_number": frame_count,
        "video_path": str(video_path),
        "keyframe_path": str(keyframe_path),
        "trigger_time": time.strftime("%Y-%m-%d %H:%M:%S")
    }

def run_tracking(source, output_dir, model_path, conf=0.7, iou=0.7,
                 alarm_before_sec=2, alarm_after_sec=4,
                 rtmp_output_url=None, enable_alarm=True):

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(output_dir / 'yolo_tracking.log')
        ]
    )

    output_dir.mkdir(parents=True, exist_ok=True)
    alarm_videos_dir = output_dir / 'alarm_videos'
    alarm_frames_dir = output_dir / 'alarm_frames'
    alarm_videos_dir.mkdir(exist_ok=True)
    alarm_frames_dir.mkdir(exist_ok=True)

    json_output = open(output_dir / 'tracking_results.jsonl', 'w')
    alarms = []
    alarm_json_path = output_dir / 'alarms.json'

    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        logging.error(f"无法打开视频源: {source}")
        return

    fps = cap.get(cv2.CAP_PROP_FPS) or 30
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) or 640
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)) or 480

    model = YOLO(model_path)

    pusher = None
    if rtmp_output_url:
        pusher = FFMpegPusher(rtmp_output_url, width, height, fps)
        pusher.start()

    frame_count = 0
    start_time = time.time()
    last_log_time = 0

    frame_buffer = deque(maxlen=int((alarm_before_sec + alarm_after_sec) * fps))
    active_alarms = {}
    alarm_id_counter = 1

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                logging.warning("视频流读取结束或出错")
                break

            frame_count += 1

            results = model.track(source=frame, conf=conf, iou=iou, tracker="bytetrack.yaml", verbose=False)

            annotated = None
            for res in results:
                annotated = res.plot()

                if res.boxes and res.boxes.id is not None:
                    for box, tid in zip(res.boxes, res.boxes.id):
                        class_id = int(box.cls.item())
                        class_name = model.names[class_id]
                        confidence = float(box.conf.item())
                        track_id = int(tid.item())

                        record = {
                            "frame_number": frame_count,
                            "track_id": track_id,
                            "class_id": class_id,
                            "class_name": class_name,
                            "confidence": round(confidence, 4),
                            "image_path": ""
                        }
                        json_output.write(json.dumps(record) + '\n')
                        json_output.flush()

                        if enable_alarm and track_id not in active_alarms:
                            active_alarms[track_id] = True
                            alarm_record = handle_alarm(
                                alarm_id=alarm_id_counter,
                                track_id=track_id,
                                class_name=class_name,
                                frame_count=frame_count,
                                frame_buffer=frame_buffer,
                                alarm_videos_dir=alarm_videos_dir,
                                alarm_frames_dir=alarm_frames_dir,
                                fps=fps,
                                width=width,
                                height=height
                            )
                            alarms.append(alarm_record)
                            logging.info(f"⚠️ 报警触发: Track {track_id}, 类别 {class_name}")
                            alarm_id_counter += 1

            if annotated is None:
                annotated = frame.copy()

            frame_buffer.append(annotated.copy())

            if pusher:
                pusher.push(annotated)

            now = time.time()
            if frame_count % 100 == 0 or now - last_log_time > 10:
                elapsed = now - start_time
                fps_real = frame_count / elapsed if elapsed > 0 else 0
                logging.info(f"帧数: {frame_count}, 实时FPS: {fps_real:.2f}")
                last_log_time = now

    except KeyboardInterrupt:
        logging.warning("用户终止程序")
    finally:
        cap.release()
        json_output.close()
        if pusher:
            pusher.stop()
        with open(alarm_json_path, 'w') as f:
            json.dump(alarms, f, indent=2)
        logging.info("跟踪任务完成 ✅")

if __name__ == '__main__':
    from pathlib import Path

    source = 'rtmp://36.212.218.114:1935/live'
    output_dir = Path('/test_wjj')
    model_path = '/root/ultralytics-main/best.pt'
    enable_alarm = True
    rtmp_output_url = 'rtmp://36.212.218.114:1935/live1'

    output_dir.mkdir(parents=True, exist_ok=True)

    run_tracking(
        source=source,
        output_dir=output_dir,
        model_path=model_path,
        enable_alarm=enable_alarm,
        rtmp_output_url=rtmp_output_url,
        conf=0.7,
        iou=0.7
    )
