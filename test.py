# -*- coding: utf-8 -*-
"""
Created on Wed Jun 25 11:19:10 2025

@author: CRSC-CS
"""
import time
import drDetP

if __name__ == "__main__":
    model_path = "./model/best0701_640_2.pt"
    initial_streams = [
        {
            "droneDeviceSn": "1581F6Q8X24BJ00G011E",
            "area": "芜湖轨道机巢",
            "monitorEq": "芜湖轨道机巢无人机",
            "aiVendorInfo": {
                "id": "1",
                "aiAlgorithmTypes": "[]"
            },
            "droneFlightMode": "",
            # "droneStreamUrl": "F:/datasets/video/厦门巡检/厦门视频20250616/官浔东向.mp4",
            "droneStreamUrl": "rtmp://172.16.5.2/stream",
            # "droneStreamUrl": "rtmp://183.162.217.222:1936/live2/stream2",
            "rootName": "中山北路",
            "gatewayDeviceSn": "7CTXMA600B02VF",
            "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
            "taskId": "test44",
            "aiStreamUrl":"rtmp://172.16.5.2/stream1",
            # "aiStreamUrl":"rtmp://183.162.217.222:1936/live3/stream4",
            "timestamp": "1750069019242"
        },
        # {
        #     "droneDeviceSn": "1581F6Q8X24BJ00G011E",
        #     "area": "芜湖轨道机巢",
        #     "monitorEq": "芜湖轨道机巢无人机",
        #     "aiVendorInfo": {
        #         "id": "1",
        #         "aiAlgorithmTypes": "[]"
        #     },
        #     "droneFlightMode": "",
        #     "droneStreamUrl": "F:/datasets/video/厦门巡检/厦门视频20250616/官浔西向.mp4",
        #     "rootName": "中山北路",
        #     "gatewayDeviceSn": "7CTXMA600B02VF",
        #     "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
        #     "taskId": "test2",
        #     "aiStreamUrl": "rtmp://192.168.0.16/stream2",
        #     "timestamp": "1750069019242"
        # }
    ]

    # 创建 StreamManager 实例并传入 initial_streams
    out_res = "./res"
    
    stream_manager = drDetP.drDetP.StreamManager(model_path)

    # 启动初始流
    
    while True:
        for stream in initial_streams:        
            stream_manager.add_stream(stream, out_res)
        print("**********************")
        time.sleep(10)
        

        
