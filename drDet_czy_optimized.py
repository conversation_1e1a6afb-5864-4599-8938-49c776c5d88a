# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS

优化版本：主要解决FPS低的问题
- 减少不必要的帧复制操作
- 优化队列管理
- 改进推理频率控制
- 减少同步阻塞
"""

import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque

from ultralytics import YOLO
import subprocess
from ultralytics.utils.plotting import Annotator, colors, save_one_box
import logging
import json

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math

import sys
from apscheduler.schedulers.blocking import BlockingScheduler

# 导入轨迹线接口
from trajectory_interface import TrajectoryRenderer

logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class multiDealImg(object):
    model = None  # 类变量，共享模型
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, model_path, save_path, dict_json):
        if multiDealImg.model is None:
            multiDealImg.model = YOLO(model_path).to('cuda')
        self.model = multiDealImg.model
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化队列大小，减少内存占用
        self.save_queue = queue.Queue(maxsize=5)  # 减小队列大小
        self.push_queue = queue.Queue(maxsize=2)  # 改为FIFO队列，减小大小
        self.frame_queue = queue.Queue(maxsize=2)  # 减小队列大小，避免延迟累积
        
        # 添加推理控制参数
        self.inference_interval = 2  # 每2帧进行一次推理
        self.frame_counter = 0
        self.last_results = None  # 缓存上次推理结果

        self.names = ["excavator", "crane", "compactor", "tank truck", "loader"]
        self.results = None
        self.dataT = 2
        
        # 告警相关
        self.buffer = deque(maxlen=75)  # 减少缓存帧数（3秒缓存，25fps）
        self.alert_flag = False
        self.alert_frames = []
        self.alert_counter = 0
        self.alert_lock = threading.Lock()
        self.alert_info = {}
        
        self.drone_state = {}
        self.drone_angle = 0
        
        # 轨迹线参数
        self.trajectory_length_multiplier = 2.0
        self.trajectory_width_ratio = 0.5
        self.manual_trajectory_length = None
        self.manual_trajectory_width = None
        
        self.trajectory_renderer = TrajectoryRenderer(
            enable_dynamic_sizing=True,
            length_multiplier=self.trajectory_length_multiplier,
            width_ratio=self.trajectory_width_ratio
        )
        
        if self.manual_trajectory_length is not None or self.manual_trajectory_width is not None:
            self.trajectory_renderer.set_manual_params(
                length=self.manual_trajectory_length,
                width=self.manual_trajectory_width
            )

    def open_ffmpeg_process(self, output_stream):
        # 优化FFmpeg参数，降低延迟
        self.command = [
            'ffmpeg',
            '-y',
            '-re',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1080),
            '-r', '25',  # 降低帧率到25fps
            '-i', '-',
            '-c:v', 'h264_nvenc',
            '-preset', 'ultrafast',  # 使用最快预设
            '-tune', 'zerolatency',  # 启用低延迟模式
            '-g', '50',  # 减小GOP大小
            '-b:v', '800k',  # 降低码率
            '-maxrate', '1M',
            '-bufsize', '1M',  # 减少缓冲区大小
            '-r', '25',
            '-pix_fmt', 'yuv420p',
            '-crf', '28',  # 提高CRF值，降低质量但提高速度
            '-f', 'flv',
            output_stream
        ]
        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, bufsize=0)

    def openFfmpegSaveVideo(self, outputSaveVideo):
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1080", "-r", "25", "-i", "-",
            "-c:v", "h264_nvenc", "-pix_fmt", "yuv420p", "-crf", "23", 
            "-preset", "fast", outputSaveVideo
        ]
        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE)

    def read_video(self, video_path):
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
    
        # 设置缓冲区大小，减少延迟
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
    
        try:
            count = 0
            skip_frames = 0  # 跳帧计数器
            
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
                
                # 每3帧处理1帧，提高性能
                skip_frames += 1
                if skip_frames % 3 != 0:
                    continue
    
                try:
                    frame = cv2.resize(frame, (1920, 1080))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 非阻塞方式放入队列
                try:
                    self.frame_queue.put_nowait(frame)
                except queue.Full:
                    # 队列满时丢弃最老的帧
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame)
                    except queue.Empty:
                        pass
                
                count += 1
                if count % 10 == 0:
                    # 更新航向角的频率也降低
                    self.drone_angle = drone_yaw_get(self.dict_json)
                    
        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def process_frame(self):
        try:
            count = 0
            alarmLevel = "2"
            alarmClass = "excavator"
    
            while self.is_switchOn:
                try:
                    # 使用超时获取帧，避免无限阻塞
                    frame = self.frame_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                    
                t1 = time.time()
                
                # 控制推理频率，不是每帧都推理
                self.frame_counter += 1
                should_infer = (self.frame_counter % self.inference_interval == 0)
                
                if should_infer:
                    # 降低推理分辨率以提高速度
                    inference_frame = cv2.resize(frame, (640, 360))
                    with self.model_lock:
                        self.results = self.model.track(
                            inference_frame, 
                            conf=0.5,  # 提高置信度阈值
                            show_conf=False, 
                            verbose=False, 
                            tracker='bytetrack.yaml'
                        )
                        self.last_results = self.results
                else:
                    # 使用缓存的推理结果
                    self.results = self.last_results
                
                t2 = time.time()
    
                # 检测告警（简化逻辑）
                alert_triggered = False
                if self.results:
                    for result in self.results:
                        if result.boxes is not None and len(result.boxes) > 0:
                            for box in result.boxes:
                                cls = int(box.cls)
                                # 简化告警检测逻辑
                                x1, y1, x2, y2 = map(int, box.xyxy[0])
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                
                                # 简化的告警判断（避免复杂计算）
                                frame_center_x = frame.shape[1] // 2
                                distance_from_center = abs(center_x - frame_center_x)
                                
                                if distance_from_center < 200:
                                    alarmLevel = "1"
                                    alert_triggered = True
                                elif distance_from_center < 400:
                                    alarmLevel = "2"
                                    alert_triggered = True
                                else:
                                    alarmLevel = "0"
                                    
                                alarmClass = self.names[cls]
                                if alert_triggered:
                                    break
                        if alert_triggered:
                            break
    
                # 简化告警处理
                with self.alert_lock:
                    if alert_triggered and not self.alert_flag:
                        self.alert_frames = list(self.buffer)
                        self.alert_flag = True
                        self.alert_counter = 0
    
                    self.buffer.append(frame.copy())
    
                    if self.alert_flag:
                        self.alert_frames.append(frame.copy())
                        self.alert_counter += 1
                        if self.alert_counter >= 125:  # 减少告警视频长度
                            self.save_alert_video(alarmLevel, alarmClass)
                            self.alert_flag = False
                            self.alert_frames = []
                            self.alert_counter = 0
    
                # 非阻塞方式放入保存队列
                try:
                    self.save_queue.put_nowait(frame.copy())
                except queue.Full:
                    pass  # 丢弃帧以避免阻塞
                
                # 创建显示帧（减少不必要的复制）
                display_frame = frame.copy()
                
                # 每5帧更新一次轨迹线（减少计算频率）
                if count % 5 == 0:
                    display_frame = self.trajectory_renderer.render_trajectory(display_frame, self.drone_angle)
                
                # 绘制检测框
                try:
                    if self.results and should_infer:  # 只在推理帧绘制检测框
                        # 需要将检测框坐标从640x360映射回1920x1080
                        scale_x = 1920 / 640
                        scale_y = 1080 / 360
                        
                        for result in self.results:
                            if result.boxes is not None:
                                for box in result.boxes:
                                    # 缩放坐标
                                    x1, y1, x2, y2 = box.xyxy[0]
                                    x1, y1, x2, y2 = int(x1*scale_x), int(y1*scale_y), int(x2*scale_x), int(y2*scale_y)
                                    cls = int(box.cls)
                                    conf = float(box.conf)
                                    
                                    # 手动绘制检测框（更高效）
                                    cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                    label = f'{self.names[cls]} {conf:.2f}'
                                    cv2.putText(display_frame, label, (x1, y1-10), 
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                except Exception as e:
                    logging.error(f"Error plotting detection results: {e}")
                
                # 非阻塞方式放入推流队列
                try:
                    self.push_queue.put_nowait(display_frame)
                except queue.Full:
                    # 丢弃最老的帧
                    try:
                        self.push_queue.get_nowait()
                        self.push_queue.put_nowait(display_frame)
                    except queue.Empty:
                        pass
                
                self.dataT = time.time() - t1
    
                if count % 100 == 0:
                    logging.info(f"Inference time: {self.dataT:.4f}s, Should infer: {should_infer}")
                count += 1
                
        except Exception as e:
            logging.error(f"Frame processing error: {e}")
    
    def pushImg(self):
        try:
            frame_count = 0
            target_fps = 25  # 目标帧率降低到25fps
            frame_interval = 1 / target_fps
            last_frame_time = time.time()
            
            while self.is_switchOn:
                t_start = time.time()
                
                try:
                    # 使用超时获取帧，避免阻塞
                    frame = self.push_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # 推流
                try:
                    if self.pipe.poll() is not None:
                        logging.error("FFmpeg process has exited, stopping push")
                        break
                    
                    self.pipe.stdin.write(frame.tobytes())
                    self.pipe.stdin.flush()  # 立即刷新缓冲区
                except Exception as e:
                    logging.error(f"Failed to write frame to pipe: {e}")
                    break
                
                frame_count += 1
                elapsed = time.time() - t_start
                
                if frame_count % 100 == 0:
                    logging.info(f"Push time: {elapsed:.4f}s, Queue size: {self.push_queue.qsize()}")
                
                # 帧率控制
                next_frame_time = last_frame_time + frame_interval
                sleep_time = max(0, next_frame_time - time.time())
                if sleep_time > 0:
                    time.sleep(sleep_time)
                last_frame_time = time.time()
                        
        except Exception as e:
            logging.error(f"Streaming error: {e}")
        finally:
            try:
                self.pipe.stdin.close()
                self.pipe.wait()
            except Exception as e:
                logging.error(f"Failed to close pipe: {e}")

    def saveImg(self):
        try:
            while self.is_switchOn or not self.save_queue.empty():
                try:
                    frame = self.save_queue.get(timeout=1.0)
                    self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                except queue.Empty:
                    continue
                except Exception as e:
                    logging.error(f"Failed to write frame to save video: {e}")
                    break
    
            # 视频保存完成后的处理
            if os.path.exists(self.save_full_video_path):
                return_video_name = minio_update.minio_interface(
                    self.dict_json, 
                    "full", 
                    os.path.basename(self.save_full_video_path), 
                    self.save_full_video_path
                )
                logging.info(f"Full video uploaded to MinIO: {return_video_name}")
            else:
                logging.error(f"Full video file not found: {self.save_full_video_path}")
    
        except Exception as e:
            logging.error(f"Video save error: {e}")
        finally:
            try:
                self.ffmpegSaveVideo.stdin.close()
                self.ffmpegSaveVideo.wait()
            except:
                pass
            logging.info("Video save process completed.")

    # 保留其他必要的方法...
    def save_alert_video(self, alarm_level, alarmClass):
        if not self.alert_frames:
            return
        
        alert_video_dir = os.path.join(self.result_path, self.video_name)
        
        if not os.path.exists(alert_video_dir):
            os.makedirs(alert_video_dir)
            logging.info(f"Created folder: {alert_video_dir}")
        
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        alert_video_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.mp4"
        )
        
        alert_image_path = os.path.join(
            alert_video_dir, 
            f"alert_{self.video_name}_{timestamp}.jpg"
        )
        
        cv2.imwrite(alert_image_path, self.alert_frames[-1])
        
        threading.Thread(
            target=self.write_alert_video, 
            args=(self.alert_frames, alert_image_path, alert_video_path, alarm_level, alarmClass)
        ).start()
        
        self.alert_info = {
            "alert_image_path": alert_image_path,
            "alert_video_path": alert_video_path,
            "full_video_path": os.path.join(self.result_path, f"{self.video_name}.mp4"),
            "stream_url": self.dict_json["droneStreamUrl"]
        }
        logging.info(f"Alert info: {json.dumps(self.alert_info)}")

    def call_minio_and_alarm(self, alert_image_path, alert_video_path, alarm_level, alarmClass):
        logging.info(f"Uploading image to MinIO: {alert_image_path}")
        return_image_name = minio_update.minio_interface(
            self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
        )
        logging.info(f"Image uploaded to MinIO: {return_image_name}")
        
        logging.info(f"Uploading video to MinIO: {alert_video_path}")
        return_video_name = minio_update.minio_interface(
            self.dict_json, "clip", os.path.basename(alert_video_path), alert_video_path
        )
        logging.info(f"Video uploaded to MinIO: {return_video_name}")
        
        logging.info(f"Posting alarm info to server")
        
        self.drone_state = drone_state_get(self.dict_json)

        drones_server.alarm_info_post(
            active_drone=self.dict_json,
            drone_state=self.drone_state,
            classes=alarmClass,
            alarmLevel=alarm_level,
            alarmImageUrl=return_image_name,
            videoUrl=return_video_name
        )
        logging.info(f"Alarm info posted to server")

    def write_alert_video(self, frames, alert_image_path, alert_video_path, alarm_level, alarmClass):
        command = [
            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24', '-s', '1920x1080', '-r', '25', '-i', '-',
            '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-crf', '23', alert_video_path
        ]
        try:
            pipe = subprocess.Popen(command, stdin=subprocess.PIPE)
            for frame in frames:
                pipe.stdin.write(frame.tobytes())
            pipe.stdin.close()
            pipe.wait()
            logging.info(f"Alert video saved: {alert_video_path}")
            
            threading.Thread(
                target=self.call_minio_and_alarm, 
                args=(alert_image_path, alert_video_path, alarm_level, alarmClass)
            ).start()
        except Exception as e:
            logging.error(f"Failed to save alert video: {e}")   
        
    def set_switch_on(self, listenVule):
        self.is_switchOn = listenVule
        
    def set_video_name(self, video_name):
        self.video_name = video_name

    def set_mission_id(self, mission_id):
        self.mission_id = mission_id

    def check_pipe_health(self):
        while self.is_switchOn:
            if self.pipe.poll() is not None:
                logging.error("FFmpeg process has exited unexpectedly.")
                self.is_switchOn = False
                break
            time.sleep(5)
            
    def startThread(self, input_stream, output_stream, saveVideoPath):
        try:
            self.open_ffmpeg_process(output_stream)
            self.openFfmpegSaveVideo(saveVideoPath)
            threads = [
                threading.Thread(target=self.read_video, args=(input_stream,)),
                threading.Thread(target=self.process_frame),
                threading.Thread(target=self.pushImg),
                threading.Thread(target=self.saveImg),
                threading.Thread(target=self.check_pipe_health)
            ]
            for t in threads:
                t.daemon = True
                t.start()
            for t in threads:
                t.join()
        except KeyboardInterrupt:
            logging.info("Received keyboard interrupt, stopping threads...")
            self.is_switchOn = False
            for t in threads:
                t.join()
        except Exception as e:
            logging.error(f"Thread start failed: {e}")

# 保留其他类的定义...
class StreamManager:
    def __init__(self, model_path):
        self.model_path = model_path
        self.streams = []
        self.lock = multiprocessing.Lock()
        self.processor = VideoStreamProcessor(model_path)
        self.active_processes = {}

    def add_stream(self, dict_json, out_res):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        with self.lock:
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path)
            )
            process.start()
            
            self.active_processes[input_path] = process
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                self.processor.stop_stream(input_url)
                process.join()
                del self.active_processes[input_url]
                logging.info(f"Stream {input_url} has been stopped.")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        with self.lock:
            return self.streams

class VideoStreamProcessor:
    def __init__(self, model_path):
        self.model_path = model_path
        self.active_detectors = {}
        self.lock = multiprocessing.Lock()

    def process_stream(self, dict_json, save_path):
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        detector = multiDealImg(self.model_path, save_path, dict_json)
        detector.set_switch_on(True)
        detector.set_video_name(video_name)
        
        with self.lock:
            self.active_detectors[input_path] = detector
        try:
            detector.startThread(input_path, output_url, save_path)
        finally:
            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]
            logging.info(f"Stream {input_path} has finished and been removed from active streams.")
    
    def is_detector_active(self, input_path):
        with self.lock:
            return input_path in self.active_detectors
        
    def stop_stream(self, input_path):
        if input_path in self.active_detectors:
            detector = self.active_detectors[input_path]
            detector.set_switch_on(False)
            del self.active_detectors[input_path]
            logging.info(f"Detector for stream {input_path} has been stopped.")
        else:
            logging.warning(f"Detector for stream {input_path} not found.")

def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)

if __name__ == "__main__":
    setup_logging()
    model_path = "./model/best_cj.pt"
    out_res = "./res"
    stream_manager = StreamManager(model_path)  
    
    keys = ["droneStreamUrl"]
    presentStream = []
    
    while True:
        login_token_get()
        active_drones_list = active_drones_get()
        drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
        
        if(len(drones_unique) > 0):
            for active_drone in drones_unique:        
              stream_manager.add_stream(active_drone, out_res)
        else:
            print(presentStream)
        presentStream = active_drones_list
        
        time.sleep(15)
