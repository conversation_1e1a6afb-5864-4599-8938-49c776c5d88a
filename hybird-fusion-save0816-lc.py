# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 19:30:02 2025

@author: CRSC-CS
"""

import cv2
import os
import time
import numpy as np
import queue
import threading
import multiprocessing
from collections import deque
from enum import Enum

from ultralytics import YOLO
import subprocess
from ultralytics.utils.plotting import Annotator, colors, save_one_box
import logging
import json

from nan import minio_update
from nan import drones_server
import nan.constants
from nan.post_request import post_requests_response
from nan.get_request import get_requests_response
from nan.drones_server import login_token_get
from nan.drones_server import active_drones_get
from nan.drones_server import drone_state_get
from nan.drones_server import alarm_info_post
from nan.drones_server import drone_yaw_get
from nan.logger_config import setup_logging

import random
import itertools
import math
import sys
from apscheduler.schedulers.blocking import BlockingScheduler

logging.basicConfig(
    format="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s",
    level=logging.INFO,
    filename="aip.log",
    filemode="a",
)

class AlgorithmMode(Enum):
    """算法模式枚举"""
    TRACKING_ONLY = "tracking_only"  # 仅目标追踪
    SEGMENTATION_ONLY = "segmentation_only"  # 仅分割
    HYBRID = "hybrid"  # 混合模式（同时运行两种算法）

class HybridConfig:
    """混合算法配置类"""
    def __init__(self):
        # 算法模式配置
        self.algorithm_mode = AlgorithmMode.HYBRID  # 默认混合模式
        
        # 模型路径配置
        self.tracking_model_path = "/home/<USER>/suanfa/czy/vis-m-bs16-sz960.pt"
        self.segmentation_model_path = "/home/<USER>/suanfa/czy/water.pt"
        
        # 类别配置
        self.tracking_classes = ["None", "people", "bicycle", "car", "van", "truck", "tricycle", "awning-tricycle", "bus", "motor"]
        self.segmentation_classes = ["water"]
        
        # 性能配置
        self.tracking_conf_threshold = 0.8
        self.segmentation_conf_threshold = 0.8
        self.tracking_inference_interval = 6  # 追踪算法推理间隔（帧数）- 减少推理频率
        self.segmentation_inference_interval = 8  # 分割算法推理间隔（帧数）- 减少推理频率
        
        # 异步执行配置
        self.enable_async_inference = True  # 启用异步推理
        self.max_inference_threads = 2  # 最大推理线程数
        
        # 结果融合配置
        self.enable_result_fusion = True  # 启用结果融合
        self.fusion_alpha = 0.7  # 融合透明度
        
        # 推流优化配置
        self.prioritize_original_stream = True  # 优先推流原始帧以保证一致性
        self.detection_overlay_ratio = 0.3  # 检测结果叠加比例（0.0-1.0）
        self.stream_quality_priority = True  # 优先保证推流质量而非检测显示
        
    def is_tracking_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.TRACKING_ONLY, AlgorithmMode.HYBRID]
    
    def is_segmentation_enabled(self):
        return self.algorithm_mode in [AlgorithmMode.SEGMENTATION_ONLY, AlgorithmMode.HYBRID]

class HybridMultiDealImg(object):
    """融合目标追踪和分割的混合检测类"""
    tracking_model = None  # 追踪模型类变量
    segmentation_model = None  # 分割模型类变量
    model_lock = threading.Lock()  # 模型推理锁

    def __init__(self, config: HybridConfig, save_path, dict_json):
        self.config = config
        
        # 初始化模型
        if self.config.is_tracking_enabled() and HybridMultiDealImg.tracking_model is None:
            HybridMultiDealImg.tracking_model = YOLO(self.config.tracking_model_path).to("cuda")
        if self.config.is_segmentation_enabled() and HybridMultiDealImg.segmentation_model is None:
            HybridMultiDealImg.segmentation_model = YOLO(self.config.segmentation_model_path).to("cuda")
        
        self.tracking_model = HybridMultiDealImg.tracking_model
        self.segmentation_model = HybridMultiDealImg.segmentation_model
        
        self.is_switchOn = False
        self.video_name = ""
        self.mission_id = ""
        self.result_path = os.path.dirname(save_path)
        self.dict_json = dict_json
        self.save_full_video_path = save_path
        
        self.lock = threading.Lock()

        # 优化队列设计 - 优先保证推流视频与原视频的一致性
        self.save_queue = queue.Queue(maxsize=10)  # 减小保存队列，避免内存积压
        self.push_queue = queue.Queue(maxsize=8)   # 增大推流队列，减少丢帧
        self.new_queue = queue.Queue(maxsize=12)   # 增大原始帧队列，确保流畅性
        self.frame_queue = queue.Queue(maxsize=3)  # 减小帧队列，降低延迟

        # 结果存储
        self.tracking_results = None
        self.segmentation_results = None
        self.dataT = 2
        
        # 异步推理相关 - 使用直接Thread管理
        self.tracking_thread = None
        self.segmentation_thread = None
        self.tracking_inference_queue = queue.Queue(maxsize=2)  # 追踪推理任务队列
        self.segmentation_inference_queue = queue.Queue(maxsize=2)  # 分割推理任务队列
        self.tracking_result_queue = queue.Queue(maxsize=2)  # 追踪结果队列
        self.segmentation_result_queue = queue.Queue(maxsize=2)  # 分割结果队列
        
        # 启动异步推理线程
        if self.config.enable_async_inference:
            self._start_inference_threads()
        
        # 追踪算法相关（基于目标ID的跟踪）
        self.target_buffers = {}  # 为每个目标ID维护独立的帧缓存
        self.active_targets = set()  # 当前活跃的目标ID集合
        self.previous_targets = set()  # 上一帧的目标ID集合
        self.target_classes = {}  # 目标ID对应的类别
        self.alert_lock = threading.Lock()
        self.alert_info = {}  # 存储告警信息
        self.buffer_maxlen = 300  # 5秒缓存（30fps）
        
        # 追踪算法告警控制
        self.alerted_targets = set()  # 已告警的目标ID集合
        self.target_first_frames = {}  # 目标ID第一次出现时的帧
        self.disappeared_targets = set()  # 已经消失过的目标ID集合
        
        # 分割算法相关
        self.segmentation_alerted_targets = set()  # 分割算法已告警的检测结果集合
        self.frame_buffer = deque(maxlen=self.buffer_maxlen)  # 全局帧缓存
        
        self.drone_state = {}
        self.drone_angle = 0
        
        # 推理计数器
        self.tracking_inference_count = 0
        self.segmentation_inference_count = 0
        
    def _start_inference_threads(self):
        """启动异步推理线程"""
        if self.config.is_tracking_enabled():
            self.tracking_thread = threading.Thread(target=self._tracking_worker, daemon=True)
            self.tracking_thread.start()
            logging.info("Tracking inference thread started")
            
        if self.config.is_segmentation_enabled():
            self.segmentation_thread = threading.Thread(target=self._segmentation_worker, daemon=True)
            self.segmentation_thread.start()
            logging.info("Segmentation inference thread started")
    
    def _tracking_worker(self):
        """追踪推理工作线程"""
        while True:
            try:
                # 获取推理任务
                frame = self.tracking_inference_queue.get(timeout=1.0)
                if frame is None:  # 停止信号
                    break
                    
                # 执行推理
                result = self._sync_tracking_inference(frame)
                
                # 将结果放入结果队列
                try:
                    self.tracking_result_queue.put_nowait((time.time(), result))
                except queue.Full:
                    # 如果队列满了，丢弃最旧的结果
                    try:
                        self.tracking_result_queue.get_nowait()
                        self.tracking_result_queue.put_nowait((time.time(), result))
                    except queue.Empty:
                        pass
                        
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Tracking worker error: {e}")
    
    def _segmentation_worker(self):
        """分割推理工作线程"""
        while True:
            try:
                # 获取推理任务
                frame = self.segmentation_inference_queue.get(timeout=1.0)
                if frame is None:  # 停止信号
                    break
                    
                # 执行推理
                result = self._sync_segmentation_inference(frame)
                
                # 将结果放入结果队列
                try:
                    self.segmentation_result_queue.put_nowait((time.time(), result))
                except queue.Full:
                    # 如果队列满了，丢弃最旧的结果
                    try:
                        self.segmentation_result_queue.get_nowait()
                        self.segmentation_result_queue.put_nowait((time.time(), result))
                    except queue.Empty:
                        pass
                        
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Segmentation worker error: {e}")

    def set_switch_on(self, switch_on):
        self.is_switchOn = switch_on
        # 如果关闭，停止推理线程
        if not switch_on and self.config.enable_async_inference:
            self._stop_inference_threads()

    def _stop_inference_threads(self):
        """停止异步推理线程"""
        try:
            # 发送停止信号
            if self.tracking_thread and self.tracking_thread.is_alive():
                self.tracking_inference_queue.put_nowait(None)
            if self.segmentation_thread and self.segmentation_thread.is_alive():
                self.segmentation_inference_queue.put_nowait(None)
        except queue.Full:
            pass

    def set_video_name(self, video_name):
        self.video_name = video_name

    def startThread(self, input_path, output_stream, save_path):
        """启动所有处理线程"""
        self.open_ffmpeg_process(output_stream)
        self.openFfmpegSaveVideo(save_path)
        
        # 启动视频读取线程
        read_thread = threading.Thread(target=self.read_video, args=(input_path,))
        read_thread.daemon = True
        read_thread.start()
        
        # 启动帧处理线程
        process_thread = threading.Thread(target=self.process_frame)
        process_thread.daemon = True
        process_thread.start()
        
        # 启动推流线程
        push_thread = threading.Thread(target=self.pushImg)
        push_thread.daemon = True
        push_thread.start()
        
        # 启动保存线程
        save_thread = threading.Thread(target=self.saveImg)
        save_thread.daemon = True
        save_thread.start()
        
        # 等待所有线程完成
        read_thread.join()
        process_thread.join()
        push_thread.join()
        save_thread.join()
        
        # 关闭FFmpeg进程
        if hasattr(self, 'pipe') and self.pipe:
            self.pipe.stdin.close()
            self.pipe.wait()
        if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:
            self.ffmpegSaveVideo.stdin.close()
            self.ffmpegSaveVideo.wait()
            
        # 关闭线程池
        if hasattr(self, 'inference_executor') and self.inference_executor:
            self.inference_executor.shutdown(wait=True)

    def open_ffmpeg_process(self, output_stream):
        """打开FFmpeg推流进程"""
        self.command = [
            'ffmpeg',
            '-y',
            '-f', 'rawvideo',
            '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', '{}x{}'.format(1920, 1280),
            '-r', '30',  # 提升到30fps输入帧率
            '-i', '-',
            '-c:v', 'h264_nvenc',
            '-preset', 'p1',        # 最快预设
            '-zerolatency', '1',    # NVENC零延迟模式
            '-g', '30',             # GOP大小匹配帧率
            '-b:v', '3M',           # 增加码率以支持30fps
            '-maxrate', '4M',       # 提高最大码率
            '-bufsize', '1M',       # 适当增加缓冲区
            '-r', '30',             # 输出30fps
            '-pix_fmt', 'yuv420p',
            '-crf', '24',           # 稍微提高质量
            '-f', 'flv',
            output_stream
        ]
        self.pipe = subprocess.Popen(self.command, stdin=subprocess.PIPE, bufsize=0)

    def openFfmpegSaveVideo(self, outputSaveVideo):
        """打开FFmpeg保存视频进程"""
        ffmpeg_command = [
            "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", "-s", "1920x1280", "-r", "30", "-i", "-",
            "-c:v", "h264_nvenc", "-pix_fmt", "yuv420p", "-crf", "23", "-preset", "fast", outputSaveVideo
        ]
        self.ffmpegSaveVideo = subprocess.Popen(ffmpeg_command, stdin=subprocess.PIPE, bufsize=0)

    def read_video(self, video_path):
        """读取视频流"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            logging.error(f"Failed to open video: {video_path}")
            return
    
        try:
            while self.is_switchOn:
                ret, frame = cap.read()
                if not ret:
                    logging.info(f"Video {video_path} ended or read failed.")
                    break
    
                if frame is None or frame.size == 0:
                    logging.warning("Empty frame received, skipping.")
                    continue
    
                try:
                    frame = cv2.resize(frame, (1920, 1280))
                except Exception as e:
                    logging.error(f"Failed to resize frame: {e}")
                    continue
    
                # 将帧放入处理队列
                if not self.frame_queue.full():
                    self.frame_queue.put(frame)
                else:
                    # 如果队列满了，移除最旧的帧以保持实时性
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put(frame)
                    except queue.Empty:
                        pass
                
                # 设置无人机角度
                self.drone_angle = 0
                
        except Exception as e:
            logging.error(f"Video read error: {e}")
        finally:
            cap.release()
            logging.info(f"Video {video_path} released.")

    def _sync_tracking_inference(self, frame):
        """同步追踪推理（在工作线程中执行）"""
        try:
            # 减少锁的持有时间，只在模型推理时加锁
            if self.tracking_model:
                with self.model_lock:
                    results = self.tracking_model.track(frame, conf=self.config.tracking_conf_threshold, verbose=False, tracker='bytetrack.yaml')
                return results
        except Exception as e:
            logging.error(f"Tracking inference error: {e}")
        return None

    def _sync_segmentation_inference(self, frame):
        """同步分割推理（在工作线程中执行）"""
        try:
            # 减少锁的持有时间，只在模型推理时加锁
            if self.segmentation_model:
                with self.model_lock:
                    results = self.segmentation_model.predict(frame, conf=self.config.segmentation_conf_threshold, verbose=False)
                return results
        except Exception as e:
            logging.error(f"Segmentation inference error: {e}")
        return None
    
    def async_tracking_inference(self, frame):
        """异步追踪推理 - 提交任务到队列"""
        try:
            self.tracking_inference_queue.put_nowait(frame.copy())
        except queue.Full:
            # 如果队列满了，丢弃最旧的任务
            try:
                self.tracking_inference_queue.get_nowait()
                self.tracking_inference_queue.put_nowait(frame.copy())
            except queue.Empty:
                pass
    
    def async_segmentation_inference(self, frame):
        """异步分割推理 - 提交任务到队列"""
        try:
            self.segmentation_inference_queue.put_nowait(frame.copy())
        except queue.Full:
            # 如果队列满了，丢弃最旧的任务
            try:
                self.segmentation_inference_queue.get_nowait()
                self.segmentation_inference_queue.put_nowait(frame.copy())
            except queue.Empty:
                pass
    
    def get_latest_tracking_result(self):
        """获取最新的追踪推理结果"""
        latest_result = None
        latest_time = 0
        
        # 获取所有可用结果，保留最新的
        while True:
            try:
                timestamp, result = self.tracking_result_queue.get_nowait()
                if timestamp > latest_time:
                    latest_time = timestamp
                    latest_result = result
            except queue.Empty:
                break
        
        return latest_result
    
    def get_latest_segmentation_result(self):
        """获取最新的分割推理结果"""
        latest_result = None
        latest_time = 0
        
        # 获取所有可用结果，保留最新的
        while True:
            try:
                timestamp, result = self.segmentation_result_queue.get_nowait()
                if timestamp > latest_time:
                    latest_time = timestamp
                    latest_result = result
            except queue.Empty:
                break
        
        return latest_result

    def process_frame(self):
        """处理帧的主函数"""
        try:
            count = 0
            last_tracking_results = None
            last_segmentation_results = None
            
            # 异步推理的Future对象
            tracking_future = None
            segmentation_future = None
            
            # 推理结果缓存
            self.tracking_results = None
            self.segmentation_results = None
    
            while self.is_switchOn:
                try:
                    frame = self.frame_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                    
                t1 = time.time()
                
                # 决定是否进行推理
                should_tracking_inference = (self.config.is_tracking_enabled() and 
                                            count % self.config.tracking_inference_interval == 0)
                should_segmentation_inference = (self.config.is_segmentation_enabled() and 
                                                count % self.config.segmentation_inference_interval == 0)
                
                # 异步推理逻辑
                # 提交追踪推理任务
                if should_tracking_inference and self.config.is_tracking_enabled():
                    if self.config.enable_async_inference:
                        self.async_tracking_inference(frame)
                        self.tracking_inference_count += 1
                    else:
                        # 同步推理
                        self.tracking_results = self._sync_tracking_inference(frame)
                        last_tracking_results = self.tracking_results
                        self.tracking_inference_count += 1
                
                # 提交分割推理任务
                if should_segmentation_inference and self.config.is_segmentation_enabled():
                    if self.config.enable_async_inference:
                        self.async_segmentation_inference(frame)
                        self.segmentation_inference_count += 1
                    else:
                        # 同步推理
                        self.segmentation_results = self._sync_segmentation_inference(frame)
                        last_segmentation_results = self.segmentation_results
                        self.segmentation_inference_count += 1
                
                # 获取异步推理结果
                if self.config.enable_async_inference:
                    # 获取最新的追踪结果
                    if self.config.is_tracking_enabled():
                        latest_tracking = self.get_latest_tracking_result()
                        if latest_tracking is not None:
                            self.tracking_results = latest_tracking
                            last_tracking_results = latest_tracking
                        elif last_tracking_results is not None:
                            self.tracking_results = last_tracking_results
                        else:
                            self.tracking_results = None
                    
                    # 获取最新的分割结果
                    if self.config.is_segmentation_enabled():
                        latest_segmentation = self.get_latest_segmentation_result()
                        if latest_segmentation is not None:
                            self.segmentation_results = latest_segmentation
                            last_segmentation_results = latest_segmentation
                        elif last_segmentation_results is not None:
                            self.segmentation_results = last_segmentation_results
                        else:
                            self.segmentation_results = None
                else:
                    # 同步模式下使用缓存结果
                    if self.config.is_tracking_enabled() and last_tracking_results is not None:
                        self.tracking_results = last_tracking_results
                    else:
                        self.tracking_results = None
                        
                    if self.config.is_segmentation_enabled() and last_segmentation_results is not None:
                        self.segmentation_results = last_segmentation_results
                    else:
                        self.segmentation_results = None
                
                # 创建融合显示帧
                display_frame = self._create_fused_display_frame(frame)
                
                # 处理追踪算法的告警
                if self.config.is_tracking_enabled() and self.tracking_results:
                    self._process_tracking_alerts(display_frame)
                
                # 处理分割算法的告警
                if self.config.is_segmentation_enabled() and self.segmentation_results:
                    self._process_segmentation_alerts(display_frame)
                
                # 保存原始帧
                try:
                    self.save_queue.put_nowait(frame)
                except queue.Full:
                    pass
                
                # 优化推流队列管理 - 优先保证原始帧流畅性
                # 将原始帧放入new_queue（用于推流的原始帧）- 减少不必要的复制
                try:
                    self.new_queue.put_nowait(frame)
                except queue.Full:
                    # 如果队列满了，移除最旧的帧
                    try:
                        self.new_queue.get_nowait()
                        self.new_queue.put_nowait(frame)
                    except queue.Empty:
                        pass
                
                # 将融合后的帧放入推流队列（仅在有检测结果时）
                if (self.tracking_results is not None or self.segmentation_results is not None):
                    try:
                        self.push_queue.put_nowait(display_frame)
                    except queue.Full:
                        # 如果检测结果队列满了，丢弃最旧的检测帧
                        try:
                            self.push_queue.get_nowait()
                            self.push_queue.put_nowait(display_frame)
                        except queue.Empty:
                            pass
                
                self.dataT = time.time() - t1
    
                if count % 200 == 199:
                    logging.info(f"Hybrid inference time: {self.dataT:.4f}s, Tracking: {self.tracking_inference_count}, Segmentation: {self.segmentation_inference_count}")
                    
                count += 1
                
        except Exception as e:
            logging.error(f"Frame processing error: {e}")

    def _create_fused_display_frame(self, frame):
        """创建融合显示帧"""
        display_frame = frame.copy()
        
        try:
            # 绘制追踪结果 - 只有在有有效结果时才绘制
            if (self.config.is_tracking_enabled() and 
                self.tracking_results is not None and 
                len(self.tracking_results) > 0 and 
                self.tracking_results[0].boxes is not None and 
                len(self.tracking_results[0].boxes) > 0):
                display_frame = self._draw_tracking_results(display_frame, self.tracking_results)
            
            # 绘制分割结果 - 只有在有有效结果时才绘制
            if (self.config.is_segmentation_enabled() and 
                self.segmentation_results is not None and 
                len(self.segmentation_results) > 0 and 
                (hasattr(self.segmentation_results[0], 'masks') and self.segmentation_results[0].masks is not None or
                 hasattr(self.segmentation_results[0], 'boxes') and self.segmentation_results[0].boxes is not None)):
                display_frame = self._draw_segmentation_results(display_frame, self.segmentation_results)
                
        except Exception as e:
            logging.error(f"Error creating fused display frame: {e}")
            display_frame = frame.copy()
        
        return display_frame

    def _draw_tracking_results(self, frame, results):
        """绘制追踪结果"""
        try:
            if results and len(results) > 0:
                # 使用plot方法绘制追踪结果
                frame = results[0].plot(img=frame, conf=True, line_width=2, font_size=0.5)
        except Exception as e:
            logging.error(f"Error drawing tracking results: {e}")
        return frame

    def _draw_segmentation_results(self, frame, results):
        """绘制分割结果"""
        try:
            if results and len(results) > 0:
                # 使用plot方法绘制分割结果（包含掩码）
                frame = results[0].plot(img=frame, conf=True, line_width=2, font_size=0.5, masks=True)
        except Exception as e:
            logging.error(f"Error drawing segmentation results: {e}")
        return frame

    def _process_tracking_alerts(self, display_frame):
        """处理追踪算法的告警"""
        current_targets = set()
        
        # 处理检测到的目标
        for result in self.tracking_results:
            if result.boxes is not None and hasattr(result.boxes, 'id') and result.boxes.id is not None:
                for j, box in enumerate(result.boxes):
                    if box.id is not None:
                        target_id = int(box.id)
                        cls = int(box.cls)
                        current_targets.add(target_id)
                        
                        # 边界检查
                        if cls >= len(self.config.tracking_classes):
                            logging.warning(f"Class index {cls} out of range for tracking_classes (max: {len(self.config.tracking_classes)-1}), using 'unknown'")
                            class_name = "unknown"
                        else:
                            class_name = self.config.tracking_classes[cls]
                        
                        # 记录目标类别
                        self.target_classes[target_id] = class_name
                        
                        # 为新目标创建缓存
                        if target_id not in self.target_buffers:
                            self.target_buffers[target_id] = deque(maxlen=self.buffer_maxlen)
                        
                        # 将带检测框的帧添加到该目标的缓存中
                        self.target_buffers[target_id].append(display_frame)
                        
                        # 检测到新目标时立即触发告警（首次检测）
                        if target_id not in self.alerted_targets:
                            self._trigger_tracking_alert(target_id, class_name, display_frame)
        
        # 检测消失的目标并保存视频
        with self.alert_lock:
            disappeared_targets = self.previous_targets - current_targets
            
            for target_id in disappeared_targets:
                if (target_id not in self.disappeared_targets and 
                    target_id in self.target_buffers and 
                    len(self.target_buffers[target_id]) > 0):
                    
                    self.disappeared_targets.add(target_id)
                    target_class = self.target_classes.get(target_id, "unknown")
                    alert_frames = list(self.target_buffers[target_id])
                    
                    # 异步保存视频
                    threading.Thread(
                        target=self._save_target_video_only,
                        args=(target_id, target_class, alert_frames),
                        daemon=True
                    ).start()
                    
                    logging.info(f"Target {target_id} ({target_class}) disappeared, saving video with {len(alert_frames)} frames")
                
                # 清理该目标的缓存
                if target_id in self.target_buffers:
                    del self.target_buffers[target_id]
                if target_id in self.target_classes:
                    del self.target_classes[target_id]
                if target_id in self.target_first_frames:
                    del self.target_first_frames[target_id]
            
            # 更新目标集合
            self.previous_targets = current_targets.copy()
            self.active_targets = current_targets.copy()

    def _process_segmentation_alerts(self, display_frame):
        """处理分割算法的告警"""
        current_detections = []
        
        # 处理检测到的目标
        for result in self.segmentation_results:
            if result.boxes is not None:
                for j, box in enumerate(result.boxes):
                    cls = int(box.cls)
                    conf = float(box.conf)
                    
                    # 边界检查
                    if cls >= len(self.config.segmentation_classes):
                        logging.warning(f"Class index {cls} out of range for segmentation_classes (max: {len(self.config.segmentation_classes)-1}), using 'unknown'")
                        class_name = "unknown"
                    else:
                        class_name = self.config.segmentation_classes[cls]
                    
                    # 为每个检测生成唯一标识（基于位置和类别）
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    detection_id = f"{cls}_{int(x1)}_{int(y1)}_{int(x2)}_{int(y2)}"
                    current_detections.append(detection_id)
                    
                    # 检测到目标时立即触发告警
                    if detection_id not in self.segmentation_alerted_targets:
                        self._trigger_segmentation_alert(detection_id, class_name, display_frame)
        
        # 将带分割掩码的帧添加到缓存中
        if not hasattr(self, 'frame_buffer'):
            self.frame_buffer = deque(maxlen=150)
        self.frame_buffer.append(display_frame)

    def _trigger_tracking_alert(self, target_id, class_name, display_frame):
        """触发追踪算法告警"""
        try:
            # 保存第一次出现时的带检测框的帧
            self.target_first_frames[target_id] = display_frame.copy()
            
            # 标记该目标已告警
            self.alerted_targets.add(target_id)
            
            # 立即保存告警图片
            alert_video_dir = os.path.join(self.result_path, self.video_name)
            if not os.path.exists(alert_video_dir):
                os.makedirs(alert_video_dir)
            
            res_backup_dir = os.path.join(self.result_path, "res", self.video_name)
            if not os.path.exists(res_backup_dir):
                os.makedirs(res_backup_dir)
            
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            alert_image_path = os.path.join(
                alert_video_dir, 
                f"tracking_target_{target_id}_{class_name}_{self.video_name}_{timestamp}.jpg"
            )
            
            res_image_path = os.path.join(
                res_backup_dir, 
                f"tracking_target_{target_id}_{class_name}_{self.video_name}_{timestamp}.jpg"
            )
            
            # 保存告警图片到两个位置
            cv2.imwrite(alert_image_path, display_frame)
            cv2.imwrite(res_image_path, display_frame)
            
            # 异步上传图片并发送告警
            threading.Thread(
                target=self._call_minio_and_alarm_immediate,
                args=(alert_image_path, f"tracking_{target_id}", class_name),
                daemon=True
            ).start()
            
            logging.info(f"Tracking target {target_id} ({class_name}) first detected - IMMEDIATE ALERT triggered")
            
        except Exception as e:
            logging.error(f"Error triggering tracking alert for target {target_id}: {e}")

    def _trigger_segmentation_alert(self, detection_id, class_name, display_frame):
        """触发分割算法告警"""
        try:
            # 标记该检测已告警
            self.segmentation_alerted_targets.add(detection_id)
            
            # 立即保存告警图片
            alert_video_dir = os.path.join(self.result_path, self.video_name)
            if not os.path.exists(alert_video_dir):
                os.makedirs(alert_video_dir)
            
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            alert_image_path = os.path.join(
                alert_video_dir, 
                f"segmentation_{detection_id}_{class_name}_{self.video_name}_{timestamp}.jpg"
            )
            
            # 保存告警图片
            cv2.imwrite(alert_image_path, display_frame)
            
            # 异步上传图片并发送告警
            threading.Thread(
                target=self._call_minio_and_alarm_immediate,
                args=(alert_image_path, f"segmentation_{detection_id}", class_name),
                daemon=True
            ).start()
            
            logging.info(f"Segmentation detection {detection_id} ({class_name}) detected - IMMEDIATE ALERT triggered")
            
        except Exception as e:
            logging.error(f"Error triggering segmentation alert for detection {detection_id}: {e}")

    def _save_target_video_only(self, target_id, target_class, alert_frames):
        """仅保存视频，不触发告警（用于目标消失时）"""
        if not alert_frames:
            return
        
        try:
            # 创建以 video_name 命名的文件夹
            alert_video_dir = os.path.join(self.result_path, self.video_name)
            
            if not os.path.exists(alert_video_dir):
                os.makedirs(alert_video_dir)
            
            res_backup_dir = os.path.join(self.result_path, "res", self.video_name)
            if not os.path.exists(res_backup_dir):
                os.makedirs(res_backup_dir)
            
            # 生成时间戳
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            
            # 告警视频路径
            alert_video_path = os.path.join(
                alert_video_dir, 
                f"tracking_target_{target_id}_{target_class}_{self.video_name}_{timestamp}_video_only.mp4"
            )
            
            res_video_path = os.path.join(
                res_backup_dir, 
                f"tracking_target_{target_id}_{target_class}_{self.video_name}_{timestamp}_video_only.mp4"
            )
            
            # 仅保存视频，不上传不告警
            threading.Thread(
                target=self._write_target_video_only, 
                args=(alert_frames, alert_video_path, res_video_path, target_id, target_class),
                daemon=True
            ).start()
            
            logging.info(f"Saving video only for target {target_id} ({target_class}): {alert_video_path}")
            
        except Exception as e:
            logging.error(f"Error saving target video for {target_id}: {e}")

    def _write_target_video_only(self, frames, alert_video_path, res_video_path, target_id, target_class):
        """写入目标视频文件"""
        for video_path in [alert_video_path, res_video_path]:
            command = [
                'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
                '-pix_fmt', 'bgr24', '-s', '1920x1280', '-r', '30', '-i', '-',
                '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-crf', '20', video_path
            ]
            try:
                pipe = subprocess.Popen(command, stdin=subprocess.PIPE, bufsize=0)
                for frame in frames:
                    pipe.stdin.write(frame.tobytes())
                pipe.stdin.close()
                pipe.wait()
                logging.info(f"Target video saved: {video_path}")
            except Exception as e:
                logging.error(f"Failed to save target video {video_path}: {e}")

    def _call_minio_and_alarm_immediate(self, alert_image_path, alert_id, target_class):
        """立即告警 - 仅上传图片并发送告警信息"""
        try:
            # 调用 minio_update 上传图片
            logging.info(f"Uploading image to MinIO: {alert_image_path}")
            return_image_name = minio_update.minio_interface(
                self.dict_json, "alarm", os.path.basename(alert_image_path), alert_image_path
            )
            logging.info(f"Image uploaded to MinIO: {return_image_name}")
            
            # 调用 alarm_info_post 发送告警
            logging.info(f"Posting immediate alarm info to server")
            
            self.drone_state = drone_state_get(self.dict_json)

            drones_server.alarm_info_post(
                active_drone=self.dict_json,
                drone_state=self.drone_state,
                classes=target_class,
                alarmLevel="1",
                alarmImageUrl=return_image_name,
                videoUrl="None"
            )
            logging.info(f"Immediate alarm posted for {alert_id} with class {target_class}")
        except Exception as e:
            logging.error(f"Failed to send immediate alarm for {alert_id}: {e}")

    def pushImg(self):
        """优化的推流图像方法 - 优先保证推流视频与原视频的一致性"""
        try:
            frame_count = 0
            last_log_time = time.time()
            last_frame_time = time.time()
            target_fps = 30
            frame_interval = 1.0 / target_fps
            last_frame = None
            last_processed_frame = None
            
            # 推流策略配置（从配置类获取）
            prioritize_original = self.config.prioritize_original_stream
            detection_overlay_ratio = self.config.detection_overlay_ratio
            frame_counter = 0
            
            while self.is_switchOn:
                current_time = time.time()
                
                # 精确的帧率控制
                time_since_last = current_time - last_frame_time
                if time_since_last < frame_interval:
                    remaining_time = frame_interval - time_since_last
                    if remaining_time > 0.001:
                        time.sleep(remaining_time)
                    continue
                
                # 简化的帧获取策略 - 减少复杂逻辑，提高性能
                frame = None
                frame_source = "none"

                try:
                    # 优先获取检测结果帧，如果没有则使用原始帧
                    if not self.push_queue.empty():
                        frame = self.push_queue.get_nowait()
                        frame_source = "detection_frame"
                    elif not self.new_queue.empty():
                        frame = self.new_queue.get_nowait()
                        frame_source = "original_frame"
                    elif last_frame is not None:
                        frame = last_frame
                        frame_source = "last_frame"
                            
                except queue.Empty:
                    if last_frame is not None:
                        frame = last_frame
                        frame_source = "emergency_fallback"
                    else:
                        time.sleep(0.001)
                        continue
                
                # 推流帧处理
                if frame is not None:
                    try:
                        # 确保帧尺寸正确
                        if frame.shape[:2] != (1280, 1920):
                            frame = cv2.resize(frame, (1920, 1280))
                        
                        self.pipe.stdin.write(frame.tobytes())
                        last_frame = frame
                        last_frame_time = current_time
                        frame_count += 1
                        frame_counter += 1
                        
                    except Exception as e:
                        logging.error(f"Error writing frame to pipe: {e}")
                        break
                else:
                    # 如果没有可用帧，短暂等待
                    time.sleep(0.001)
                
                # 简化的队列监控 - 减少复杂的动态调整逻辑
                if frame_counter % 300 == 0:  # 每10秒评估一次
                    push_queue_size = self.push_queue.qsize()
                    new_queue_size = self.new_queue.qsize()

                    # 简单的队列清理策略
                    if push_queue_size > 5:
                        # 清理过多的检测帧，保持流畅性
                        while self.push_queue.qsize() > 2:
                            try:
                                self.push_queue.get_nowait()
                            except queue.Empty:
                                break
                
                # 每10秒打印一次统计信息
                if current_time - last_log_time >= 10:
                    actual_fps = frame_count / (current_time - last_log_time)
                    push_queue_size = self.push_queue.qsize()
                    new_queue_size = self.new_queue.qsize()
                    
                    logging.info(f"Push FPS: {actual_fps:.2f}, Strategy: {'Original Priority' if prioritize_original else 'Detection Priority'}, "
                               f"Overlay ratio: {detection_overlay_ratio:.1%}, Push queue: {push_queue_size}, New queue: {new_queue_size}, Last source: {frame_source}")
                    
                    frame_count = 0
                    last_log_time = current_time
                    
        except Exception as e:
            logging.error(f"Push image error: {e}")
        finally:
            if hasattr(self, 'pipe') and self.pipe:
                try:
                    self.pipe.stdin.close()
                except:
                    pass

    def saveImg(self):
        """保存图像"""
        try:
            while self.is_switchOn:
                try:
                    frame = self.save_queue.get(timeout=1.0)
                    if frame is not None:
                        self.ffmpegSaveVideo.stdin.write(frame.tobytes())
                except queue.Empty:
                    continue
                except Exception as e:
                    logging.error(f"Error saving frame: {e}")
                    break
                    
        except Exception as e:
            logging.error(f"Save image error: {e}")
        finally:
            if hasattr(self, 'ffmpegSaveVideo') and self.ffmpegSaveVideo:
                try:
                    self.ffmpegSaveVideo.stdin.close()
                except:
                    pass


class HybridStreamManager:
    """混合算法流管理器"""
    def __init__(self, config: HybridConfig):
        self.config = config
        self.processor = HybridVideoStreamProcessor(config)
        self.active_processes = {}
        self.active_streams = {}  # 用于记录正在运行的流地址
        self.streams = []
        self.lock = threading.Lock()

    def add_stream(self, dict_json, out_res):
        """添加流"""
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        with self.lock:
            if input_path in self.active_processes:
                logging.warning(f"Stream {input_path} is already running.")
                return
            
            # 创建以 video_name 命名的文件夹
            save_folder = os.path.join(out_res, video_name)
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                logging.info(f"Created folder: {save_folder}")

            # 设置保存路径
            save_path = os.path.join(save_folder, f"{video_name}.mp4")

            # 启动新流的处理进程
            process = multiprocessing.Process(
                target=self.processor.process_stream,
                args=(dict_json, save_path)
            )
            process.start()
            
            # 存储进程和流地址
            self.active_processes[input_path] = process
            self.active_streams[input_path] = True
            self.streams.append({
                "input": input_path,
                "output": output_url,
                "save_path": save_path,
                "video_name": video_name
            })
            logging.info(f"Hybrid stream {output_url} has been added and started.")

    def stop_stream(self, input_url):
        """停止流"""
        with self.lock:
            if input_url in self.active_processes:
                process = self.active_processes[input_url]
                self.processor.stop_stream(input_url)
                process.join()
                del self.active_processes[input_url]
                if input_url in self.active_streams:
                    del self.active_streams[input_url]
                logging.info(f"Stream {input_url} has been stopped.")
            else:
                logging.warning(f"Stream {input_url} not found.")

    def get_streams(self):
        """获取流列表"""
        with self.lock:
            return self.streams
    
    def is_stream_active(self, output_url):
        """检查流是否活跃"""
        with self.lock:
            return output_url in self.active_streams


class HybridVideoStreamProcessor:
    """混合算法视频流处理器"""
    
    def __init__(self, config: HybridConfig):
        self.config = config
        self.active_detectors = {}
        self.lock = multiprocessing.Lock()

    def process_stream(self, dict_json, save_path):
        """处理流"""
        input_path = dict_json["droneStreamUrl"]
        output_url = dict_json["aiStreamUrl"]
        video_name = dict_json["taskId"]
        
        detector = HybridMultiDealImg(self.config, save_path, dict_json)
        detector.set_switch_on(True)
        detector.set_video_name(video_name)
        
        with self.lock:
            self.active_detectors[input_path] = detector
        try:
            detector.startThread(input_path, output_url, save_path)
        finally:
            with self.lock:
                if input_path in self.active_detectors:
                    del self.active_detectors[input_path]
            logging.info(f"Hybrid stream {input_path} has finished and been removed.")

    def stop_stream(self, input_path):
        """停止流"""
        if input_path in self.active_detectors:
            detector = self.active_detectors[input_path]
            detector.set_switch_on(False)
            del self.active_detectors[input_path]
            logging.info(f"Hybrid detector for stream {input_path} has been stopped.")
        else:
            logging.warning(f"Hybrid detector for stream {input_path} not found.")
    
    def is_detector_active(self, input_path):
        """检查检测器是否活跃"""
        with self.lock:
            return input_path in self.active_detectors


# 定义比较函数，基于指定的关键字 
def is_equal(dict1, dict2, keys):
    return all(dict1.get(key) == dict2.get(key) for key in keys)


if __name__ == "__main__":
    setup_logging()
    
    # 创建混合算法配置
    config = HybridConfig()
    
    # 配置算法模式（可以根据需要修改）
    # config.algorithm_mode = AlgorithmMode.TRACKING_ONLY  # 仅追踪
    config.algorithm_mode = AlgorithmMode.SEGMENTATION_ONLY  # 仅分割
    # config.algorithm_mode = AlgorithmMode.HYBRID  # 混合模式（默认）
    
    # 配置模型路径
    config.tracking_model_path = "/home/<USER>/suanfa/czy/vis-m-bs16-sz960.pt"
    config.segmentation_model_path = "/home/<USER>/suanfa/czy/water.pt"
    
    # 推理间隔配置
    config.tracking_inference_interval = 2  # 每2帧推理一次追踪
    config.segmentation_inference_interval = 3  # 每3帧推理一次分割
    
    # 启用异步推理
    config.enable_async_inference = True
    config.max_inference_threads = 2
    
    out_res = "./res"
    stream_manager = HybridStreamManager(config)
    
    # 定义关键字列表
    keys = ["droneStreamUrl"]
    
    # active_drones_list = [
    #     {
    #         "droneDeviceSn": "1581F6Q8X24BJ00G011E",
    #         "area": "芜湖轨道机巢",
    #         "monitorEq": "芜湖轨道机巢无人机",
    #         "aiVendorInfo": {
    #             "id": "1",
    #             "aiAlgorithmTypes": "[]"
    #         },
    #         "droneFlightMode": "30",
    #         "droneStreamUrl": "rtmp://10.254.153.107:1935/live2/stream2",
    #         "rootName": "中山北路",
    #         "gatewayDeviceSn": "7CTXMA600B02VF",
    #         "uuid": "704b7f69-5f50-4d07-8631-98438ca40127",
    #         "taskId": "hybrid_fusion_test",
    #         "aiStreamUrl": "rtmp://10.254.153.107:1935/live3/stream3",
    #         "timestamp": "1750069019242"
    #     }
    # ]
    
    presentStream = []
    while True:
        # 获取正在飞行任务中的无人机列表
        # active_drones_list = active_drones_get()
        #获取token
        login_token_get()
        #获取正在飞行任务中的无人机列表
        active_drones_list = active_drones_get()
        # 获取 active_drones_list 中有且 presentStream 中没有的元素
        drones_unique = [item for item in active_drones_list if not any(is_equal(item, a_item, keys) for a_item in presentStream)]
        
        if(len(drones_unique) > 0):
            for active_drone in drones_unique:        
                stream_manager.add_stream(active_drone, out_res)
        else:
            print("active_processes = ", presentStream)
            stream_manager.active_processes = {}
        presentStream = active_drones_list
        
        time.sleep(15)

### 1. 推流策略优化
# - 原始帧优先策略 ：优先推流原始帧，确保视频流畅性和一致性
# - 智能检测叠加 ：可配置的检测结果叠加比例（默认30%），避免检测结果过度干扰视频流
# - 动态策略调整 ：根据队列状态自动调整推流策略，防止检测结果积压
# ### 2. 队列管理优化
# - 增大原始帧队列 ： new_queue 从3增加到8，确保原始帧充足供应
# - 减小检测队列 ： push_queue 从5减少到3，避免检测结果积压
# - 智能队列清理 ：队列满时自动移除最旧帧，保持实时性
# ### 3. 帧率控制优化
# - 精确帧率控制 ：改进时间控制逻辑，确保稳定的30fps输出
# - 帧尺寸检查 ：自动检查和调整帧尺寸，确保推流兼容性
# - 异常处理增强 ：更好的错误处理和恢复机制
# ### 4. 配置化管理
# 在 HybridConfig 类中新增了推流优化配置：
# - prioritize_original_stream ：是否优先推流原始帧
# - detection_overlay_ratio ：检测结果叠加比例（0.0-1.0）
# - stream_quality_priority ：是否优先保证推流质量
# ### 5. 处理流程优化
# - 分离原始帧和检测帧 ：在 process_frame 中分别管理原始帧和检测结果帧
# - 条件性检测显示 ：仅在有检测结果时才将融合帧放入检测队列
# - 实时监控 ：增强的日志输出，实时监控推流状态和策略