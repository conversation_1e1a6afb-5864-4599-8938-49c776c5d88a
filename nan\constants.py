# constants.py


#算法厂商名（英文字母小写）
aiVendorName = "算法厂商3"
#登录token缓存
constant_token = ""

baseURL = "http://192.168.0.7:8086"
# baseURL = "http://172.16.5.1:8089"
loginAddr = "/jeecg-boot/sys/login/simple"
activeAddr = "/jeecg-boot/sys/api/ai/active-drones"
stateAddr = "/jeecg-boot/sys/api/drone"
alarmAddr = "/jeecg-boot/sys/api/ai-alarm"

class loginInfo:
    username = "admin_sub"
    password = "123456.a"
    captcha = "e1a709144444b0800585121bb9272318"
    checkKey = "e352eaa3205126f521c027784ce82baf"


class alarmInfo:
    #巡检区域
    deviceType = 1
    aiAlgorithmVendorId = 3
    taskType = 1
    
class minioInfo:
    # MinIO配置
    # endpoint = "172.16.5.1:9000"
    endpoint = "183.162.217.222:9001"
    access_key = "admin"
    secret_key = "admin123"
    secure = False
    bucket_name = "prod-ai-media"

class yawInfo:
    his_yaw = 1000
    key_yaw = "gimbal_yaw"
